package com.bxm.customer.mapper;

import java.util.List;

import com.bxm.customer.domain.CBusinessAccounting;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 会计Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
@Mapper
public interface CBusinessAccountingMapper extends BaseMapper<CBusinessAccounting>
{
    /**
     * 查询会计
     * 
     * @param id 会计主键
     * @return 会计
     */
    public CBusinessAccounting selectCBusinessAccountingById(Long id);

    /**
     * 查询会计列表
     * 
     * @param cBusinessAccounting 会计
     * @return 会计集合
     */
    public List<CBusinessAccounting> selectCBusinessAccountingList(CBusinessAccounting cBusinessAccounting);

    /**
     * 新增会计
     * 
     * @param cBusinessAccounting 会计
     * @return 结果
     */
    public int insertCBusinessAccounting(CBusinessAccounting cBusinessAccounting);

    /**
     * 修改会计
     * 
     * @param cBusinessAccounting 会计
     * @return 结果
     */
    public int updateCBusinessAccounting(CBusinessAccounting cBusinessAccounting);

    /**
     * 删除会计
     * 
     * @param id 会计主键
     * @return 结果
     */
    public int deleteCBusinessAccountingById(Long id);

    /**
     * 批量删除会计
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCBusinessAccountingByIds(Long[] ids);
}
