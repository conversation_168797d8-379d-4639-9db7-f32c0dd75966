package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.vo.valueAdded.SocialInsuranceVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.helper.ValueAddedEmpValidationHelper;
import com.bxm.customer.service.strategy.AbstractEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 社医保业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class SocialInsuranceUpsertStrategy extends AbstractEmployeeUpsertStrategy {

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.SOCIAL_INSURANCE.getCode();
    }

    @Override
    protected void validateBusinessFields(ValueAddedEmployeeVO employeeVO) {
        // 1. 执行通用业务字段校验
        validateCommonBusinessFields(employeeVO);

        // 2. 社医保业务特有校验
        if (!ValueAddedEmpValidationHelper.isReductionOperation(employeeVO.getOperationType())) {
            // 验证应发工资（社医保业务通常需要工资信息）
            if (employeeVO.getGrossSalary() == null || employeeVO.getGrossSalary().doubleValue() < 0) {
                throw new IllegalArgumentException("社医保业务应发工资不能为空且必须为非负数");
            }
        }
    }

    @Override
    protected void preprocessEmployee(ValueAddedEmployee employee) {
        // 1. 执行通用预处理
        super.preprocessEmployee(employee);

        // 2. 社医保业务特有预处理
        // 如果没有提供社保信息，设置默认值
        if (employee.getSocialInsurance() == null) {
            employee.setSocialInsurance(getDefaultSocialInsurance());
        }
    }

    @Override
    protected ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 社医保业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        return findExistingEmployeeByIdNumber(employee, ValueAddedBizType.SOCIAL_INSURANCE.getCode());
    }

    @Override
    protected ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 调用父类的通用合并逻辑（已包含社医保需要的所有字段合并）
        return super.mergeEmployee(existing, newEmployee);
    }

    /**
     * 获取默认的社保信息
     *
     * @return 默认社保信息对象
     */
    private SocialInsuranceVO getDefaultSocialInsurance() {
        return SocialInsuranceVO.createDefault();
    }
}
