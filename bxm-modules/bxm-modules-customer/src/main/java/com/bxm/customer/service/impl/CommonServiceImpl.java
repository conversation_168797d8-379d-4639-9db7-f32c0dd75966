package com.bxm.customer.service.impl;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.service.CommonService;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.RemoteEmployeeService;
import com.bxm.system.api.domain.SysDept;
import com.bxm.system.api.domain.SysEmployee;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CommonServiceImpl implements CommonService {

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteEmployeeService remoteEmployeeService;

    @Override
    public List<Long> getAllChildrenDeptIdsByDeptIds(Long deptId, String deptIds) {
        Set<Long> deptIdsSet = new HashSet<>();
        if (!Objects.isNull(deptId)) {
            deptIdsSet.addAll(remoteDeptService.getAllChildrenIdByTopDeptId(deptId).getDataThrowException());
        }
        if (!StringUtils.isEmpty(deptIds)) {
            for (String deptIdStr : deptIds.split(",")) {
                deptIdsSet.addAll(remoteDeptService.getAllChildrenIdByTopDeptId(Long.parseLong(deptIdStr)).getDataThrowException());
            }
        }
        return ObjectUtils.isEmpty(deptIdsSet) ? Lists.newArrayList() : new ArrayList<>(deptIdsSet);
    }

    @Override
    public String getDeptEmployeeByDeptId(Long deptId) {
        if (Objects.isNull(deptId)) {
            return "";
        }
        SysDept sysDept = remoteDeptService.getDeptInfo(deptId).getDataThrowException();
        List<SysEmployee> employeeList = remoteEmployeeService.getEmployeeListByDeptId(deptId).getDataThrowException();
        StringBuilder result = new StringBuilder();
        if (!Objects.isNull(sysDept)) {
            result.append(sysDept.getDeptName());
        }
        if (!ObjectUtils.isEmpty(employeeList)) {
            result.append("（").append(employeeList.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(","))).append("）");
        }
        return result.toString();
    }

    @Override
    public Map<Long, String> getBatchDeptEmployeeByDeptIds(List<Long> deptIds) {
        if (ObjectUtils.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }
        List<SysDept> deptList = remoteDeptService.getByDeptIds(deptIds).getDataThrowException();
        Map<Long, SysDept> deptMap = ObjectUtils.isEmpty(deptList) ? Maps.newHashMap() :
                deptList.stream().collect(Collectors.toMap(SysDept::getDeptId, Function.identity()));
        List<SysEmployee> employeeList = remoteEmployeeService.getBatchEmployeeByDeptIds(deptIds).getDataThrowException();
        Map<Long, List<SysEmployee>> employeeMap = ObjectUtils.isEmpty(employeeList) ? Maps.newHashMap() :
                employeeList.stream().collect(Collectors.groupingBy(SysEmployee::getDeptId));
        return deptIds.stream().collect(Collectors.toMap(row -> row, row -> {
            SysDept dept = deptMap.get(row);
            List<SysEmployee> employees = employeeMap.get(row);
            return (Objects.isNull(dept) ? "" : dept.getDeptName()) + (ObjectUtils.isEmpty(employees) ? "" :
                    ("（" + employees.stream().map(SysEmployee::getEmployeeName).collect(Collectors.joining(",")) + "）"));
        }));
    }
}
