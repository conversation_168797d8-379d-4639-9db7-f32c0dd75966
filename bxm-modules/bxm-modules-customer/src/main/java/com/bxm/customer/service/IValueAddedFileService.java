package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.enums.ValueAddedFileType;
import com.bxm.customer.domain.vo.valueAdded.StockUploadResultVO;
import com.bxm.file.api.domain.ValueAddedFileDTO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 增值交付单文件Service接口
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
public interface IValueAddedFileService extends IService<ValueAddedFile> {

    /**
     * 保存人员明细Excel文件
     *
     * @param excelFile Excel文件
     * @param deliveryOrderNo 交付单编号（可选）
     * @return 文件ID
     * @throws Exception 当文件处理失败时抛出
     */
    Long savePersonnelExcelFile(MultipartFile excelFile, String deliveryOrderNo) throws Exception;

    /**
     * 保存交付单文件
     *
     * @param file 文件
     * @param deliveryOrderNo 交付单编号
     * @return 文件ID
     * @throws Exception 当文件处理失败时抛出
     */
    Long saveDeliveryOrderFile(MultipartFile file, String deliveryOrderNo) throws Exception;

    /**
     * 保存库存Excel文件
     *
     * @param excelFile Excel文件
     * @param deliveryOrderNo 交付单编号
     * @return 库存文件上传结果，包含解析后的库存数据列表和文件上传结果
     * @throws Exception 当文件处理失败时抛出
     */
    StockUploadResultVO saveStockExcelFile(MultipartFile excelFile, String deliveryOrderNo) throws Exception;

    /**
     * 删除文件（软删除）
     *
     * @param fileId 文件ID
     * @param deliveryOrderNo 交付单编号（用于权限验证）
     * @return 是否删除成功
     */
    boolean deleteFile(Long fileId, String deliveryOrderNo);

    /**
     * 获取文件下载URL
     *
     * @param fileId 文件ID
     * @param deliveryOrderNo 交付单编号（用于权限验证）
     * @return 文件下载URL
     */
    String getFileDownloadUrl(Long fileId, String deliveryOrderNo);

    /**
     * 更新文件处理状态
     *
     * @param fileId 文件ID
     * @param status 处理状态：0-处理中，1-处理完成，2-处理失败
     * @param statusInfo 状态详细信息（JSON格式）
     */
    void updateFileProcessStatus(Long fileId, Integer status, String statusInfo);

    /**
     * 获取文件处理状态
     *
     * @param fileId 文件ID
     * @return 状态信息（JSON格式）
     */
    String getFileProcessStatus(Long fileId);

    /**
     * 根据文件ID获取文件信息
     *
     * @param fileId 文件ID
     * @return 文件信息
     */
    ValueAddedFile getFileById(Long fileId);

    /**
     * 上传文件
     *
     * @param deliveryOrderNo 交付单编号
     * @param file 要上传的文件
     * @param fileType 文件类型
     * @return 上传后的文件信息，包含fileId
     * @throws Exception 当文件上传失败时抛出
     */
    ValueAddedFileDTO uploadFile(String deliveryOrderNo, MultipartFile file, ValueAddedFileType fileType) throws Exception;

    /**
     * 查询交付单附件
     * @param deliveryOrderNo 交付单编号
     * @param fileTypes 附件类型
     * @return
     */
    List<ValueAddedFile> getByDeliveryOrderNoAndFileTypes(String deliveryOrderNo, Integer... fileTypes);

    /**
     * 查询交付单附件
     * @param deliveryOrderNo 交付单编号
     * @param fileTypes 附件类型
     * @return
     */
    void deleteFileByDeliveryOrderNoAndFileTypes(String deliveryOrderNo, Integer... fileTypes);
}
