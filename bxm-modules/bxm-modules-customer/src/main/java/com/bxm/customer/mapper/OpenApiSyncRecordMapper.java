package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.OpenApiSyncRecord;

/**
 * 第三方申报同步Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Mapper
public interface OpenApiSyncRecordMapper extends BaseMapper<OpenApiSyncRecord>
{
    /**
     * 查询第三方申报同步
     * 
     * @param id 第三方申报同步主键
     * @return 第三方申报同步
     */
    public OpenApiSyncRecord selectOpenApiSyncRecordById(Long id);

    /**
     * 查询第三方申报同步列表
     * 
     * @param openApiSyncRecord 第三方申报同步
     * @return 第三方申报同步集合
     */
    public List<OpenApiSyncRecord> selectOpenApiSyncRecordList(OpenApiSyncRecord openApiSyncRecord);

    /**
     * 新增第三方申报同步
     * 
     * @param openApiSyncRecord 第三方申报同步
     * @return 结果
     */
    public int insertOpenApiSyncRecord(OpenApiSyncRecord openApiSyncRecord);

    /**
     * 修改第三方申报同步
     * 
     * @param openApiSyncRecord 第三方申报同步
     * @return 结果
     */
    public int updateOpenApiSyncRecord(OpenApiSyncRecord openApiSyncRecord);

    /**
     * 删除第三方申报同步
     * 
     * @param id 第三方申报同步主键
     * @return 结果
     */
    public int deleteOpenApiSyncRecordById(Long id);

    /**
     * 批量删除第三方申报同步
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiSyncRecordByIds(Long[] ids);
}
