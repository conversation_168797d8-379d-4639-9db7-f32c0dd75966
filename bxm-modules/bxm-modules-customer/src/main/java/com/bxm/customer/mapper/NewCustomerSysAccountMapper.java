package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerSysAccount;

/**
 * 新户流转系统账号Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerSysAccountMapper extends BaseMapper<NewCustomerSysAccount>
{
    /**
     * 查询新户流转系统账号
     * 
     * @param id 新户流转系统账号主键
     * @return 新户流转系统账号
     */
    public NewCustomerSysAccount selectNewCustomerSysAccountById(Long id);

    /**
     * 查询新户流转系统账号列表
     * 
     * @param newCustomerSysAccount 新户流转系统账号
     * @return 新户流转系统账号集合
     */
    public List<NewCustomerSysAccount> selectNewCustomerSysAccountList(NewCustomerSysAccount newCustomerSysAccount);

    /**
     * 新增新户流转系统账号
     * 
     * @param newCustomerSysAccount 新户流转系统账号
     * @return 结果
     */
    public int insertNewCustomerSysAccount(NewCustomerSysAccount newCustomerSysAccount);

    /**
     * 修改新户流转系统账号
     * 
     * @param newCustomerSysAccount 新户流转系统账号
     * @return 结果
     */
    public int updateNewCustomerSysAccount(NewCustomerSysAccount newCustomerSysAccount);

    /**
     * 删除新户流转系统账号
     * 
     * @param id 新户流转系统账号主键
     * @return 结果
     */
    public int deleteNewCustomerSysAccountById(Long id);

    /**
     * 批量删除新户流转系统账号
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerSysAccountByIds(Long[] ids);
}
