package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.OpenApiSyncCustomer;

/**
 * 第三方申报同步客户Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Mapper
public interface OpenApiSyncCustomerMapper extends BaseMapper<OpenApiSyncCustomer>
{
    /**
     * 查询第三方申报同步客户
     * 
     * @param id 第三方申报同步客户主键
     * @return 第三方申报同步客户
     */
    public OpenApiSyncCustomer selectOpenApiSyncCustomerById(Long id);

    /**
     * 查询第三方申报同步客户列表
     * 
     * @param openApiSyncCustomer 第三方申报同步客户
     * @return 第三方申报同步客户集合
     */
    public List<OpenApiSyncCustomer> selectOpenApiSyncCustomerList(OpenApiSyncCustomer openApiSyncCustomer);

    /**
     * 新增第三方申报同步客户
     * 
     * @param openApiSyncCustomer 第三方申报同步客户
     * @return 结果
     */
    public int insertOpenApiSyncCustomer(OpenApiSyncCustomer openApiSyncCustomer);

    /**
     * 修改第三方申报同步客户
     * 
     * @param openApiSyncCustomer 第三方申报同步客户
     * @return 结果
     */
    public int updateOpenApiSyncCustomer(OpenApiSyncCustomer openApiSyncCustomer);

    /**
     * 删除第三方申报同步客户
     * 
     * @param id 第三方申报同步客户主键
     * @return 结果
     */
    public int deleteOpenApiSyncCustomerById(Long id);

    /**
     * 批量删除第三方申报同步客户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiSyncCustomerByIds(Long[] ids);
}
