package com.bxm.customer.service.strategy;

import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.helper.ValueAddedEmpValidationHelper;
import com.bxm.customer.service.IValueAddedEmployeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

/**
 * 增值员工upsert策略抽象基类
 *
 * 提供通用的 upsert 流程实现，子类只需实现业务特定的验证和处理逻辑。
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
public abstract class AbstractEmployeeUpsertStrategy implements ValueAddedEmployeeUpsertStrategy {

    @Autowired
    protected IValueAddedEmployeeService valueAddedEmployeeService;

    @Autowired
    protected ValueAddedEmpValidationHelper validationHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long upsert(ValueAddedEmployeeVO employeeVO) {
        try {
            log.info("Starting upsert operation for employee: {}, bizType: {}",
                    employeeVO.getEmployeeName(), employeeVO.getBizType());

            // 1. 业务特定字段验证
            validateBusinessFields(employeeVO);

            // 2. VO转换为DO
            ValueAddedEmployee employee = convertVoToEntity(employeeVO);

            // 3. 数据预处理
            preprocessEmployee(employee);

            // 4. 查找现有记录
            ValueAddedEmployee existingEmployee = findExistingEmployee(employee);

            boolean isUpdate = existingEmployee != null;
            ValueAddedEmployee targetEmployee;

            if (isUpdate) {
                // 更新操作：合并数据
                targetEmployee = mergeEmployee(existingEmployee, employee);

                // 执行更新
                if (!valueAddedEmployeeService.updateById(targetEmployee)) {
                    throw new RuntimeException("Failed to update employee: " + targetEmployee.getId());
                }
                log.info("Employee updated successfully: ID={}, Name={}",
                        targetEmployee.getId(), targetEmployee.getEmployeeName());
            } else {
                // 插入操作
                targetEmployee = employee;
                // 执行插入
                if (!valueAddedEmployeeService.save(targetEmployee)) {
                    throw new RuntimeException("Failed to insert employee: " + targetEmployee.getEmployeeName());
                }
                log.info("Employee inserted successfully: ID={}, Name={}",
                        targetEmployee.getId(), targetEmployee.getEmployeeName());
            }

            return targetEmployee.getId();

        } catch (IllegalArgumentException e) {
            log.error("Validation failed during upsert operation for employee: {}, bizType: {}, error: {}",
                    employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage());
            throw e;
        } catch (RuntimeException e) {
            log.error("Runtime error during upsert operation for employee: {}, bizType: {}, error: {}",
                    employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during upsert operation for employee: {}, bizType: {}, error: {}",
                    employeeVO.getEmployeeName(), employeeVO.getBizType(), e.getMessage(), e);
            throw new RuntimeException("Upsert operation failed for employee " + employeeVO.getEmployeeName() + ": " + e.getMessage(), e);
        }
    }

    /**
     * VO转换为Entity
     *
     * @param employeeVO 员工信息VO
     * @return 员工信息Entity
     */
    protected ValueAddedEmployee convertVoToEntity(ValueAddedEmployeeVO employeeVO) {
        ValueAddedEmployee employee = new ValueAddedEmployee();
        BeanUtils.copyProperties(employeeVO, employee);

        // 设置业务类型
        employee.setBizType(getSupportedBizType());

        return employee;
    }


    /**
     * 验证员工信息的业务特定字段
     * 子类必须实现此方法
     *
     * @param employeeVO 员工信息VO
     * @throws IllegalArgumentException 当验证失败时抛出
     */
    protected abstract void validateBusinessFields(ValueAddedEmployeeVO employeeVO);

    /**
     * 通用业务字段校验
     * 包含所有业务类型共同的校验逻辑，子类可直接调用
     *
     * @param employeeVO 员工信息VO
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    protected void validateCommonBusinessFields(ValueAddedEmployeeVO employeeVO) {
        // 1. 减员操作跳过业务规则校验，只需要基本信息即可
        if (ValueAddedEmpValidationHelper.isReductionOperation(employeeVO.getOperationType())) {
            return;
        }

        // 2. 校验姓名（必填，2-50个字，至少填2个字）
        ValueAddedEmpValidationHelper.validateEmployeeName(employeeVO.getEmployeeName());

        // 3. 校验身份证号（必填，18个数字）
        ValueAddedEmpValidationHelper.validateIdNumberStrict(employeeVO.getIdNumber());

        // 4. 校验手机号（必填，11个数字）- 仅增员和更正需要
        ValueAddedEmpValidationHelper.validateMobile(employeeVO.getMobile());

        // 5. 校验同一交付单内的唯一性（身份证号和手机号）- 仅增员和更正需要
        validationHelper.validateUniqueness(employeeVO, getSupportedBizType());

        // 6. 校验申报基数（方式为增员或更正时必填，浮点2位数字）
        ValueAddedEmpValidationHelper.validateSocialInsuranceBase(
                employeeVO.getSocialInsuranceBase(), employeeVO.getOperationType());
    }

    /**
     * 通用数据预处理
     * 包含所有业务类型共同的预处理逻辑
     *
     * @param employee 员工信息Entity
     */
    protected void preprocessCommonFields(ValueAddedEmployee employee) {
        // 1. 标准化身份证号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getIdNumber())) {
            employee.setIdNumber(employee.getIdNumber().trim().toUpperCase());
        }
    }

    /**
     * 预处理员工信息
     * 子类可以重写此方法实现特定的预处理逻辑
     *
     * @param employee 员工信息Entity
     */
    protected void preprocessEmployee(ValueAddedEmployee employee) {
        // 默认调用通用预处理
        preprocessCommonFields(employee);
    }

    /**
     * 通过身份证号或手机号查找现有员工记录（通用方法）
     * 优先根据身份证号查找，如果没找到再根据手机号查找
     *
     * @param employee 员工信息Entity
     * @param bizType 业务类型
     * @return 现有的员工记录，如果不存在则返回null
     */
    protected ValueAddedEmployee findExistingEmployeeByIdNumber(ValueAddedEmployee employee, Integer bizType) {
        // 使用OR条件一次查询身份证号或手机号匹配的记录
        return valueAddedEmployeeService.getByDeliveryOrderAndIdNumberOrMobile(
                employee.getDeliveryOrderNo(),
                employee.getIdNumber(),
                employee.getMobile(),
                bizType);
    }

    /**
     * 查找现有员工记录
     * 子类必须实现此方法
     *
     * @param employee 员工信息Entity
     * @return 现有的员工记录，如果不存在则返回null
     */
    protected abstract ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee);



    /**
     * 合并员工信息
     * 提供通用的合并逻辑，子类可以重写实现特定的合并逻辑
     *
     * @param existing 现有员工记录
     * @param newEmployee 新的员工信息
     * @return 合并后的员工信息
     */
    protected ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 1. 使用BeanUtils复制基础字段，但排除ID和创建时间等不应被覆盖的字段
        String[] ignoreProperties = {"id", "createTime", "createBy"};
        BeanUtils.copyProperties(newEmployee, existing, ignoreProperties);

        // 2. 通用字段的非空值复制（适用于社医保和个税明细）
        mergeCommonFields(existing, newEmployee);

        return existing;
    }

    /**
     * 合并通用字段
     * 包含社医保和个税明细业务都需要的字段合并逻辑
     *
     * @param existing 现有员工记录
     * @param newEmployee 新的员工信息
     */
    protected void mergeCommonFields(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 应发工资
        if (newEmployee.getGrossSalary() != null) {
            existing.setGrossSalary(newEmployee.getGrossSalary());
        }
        // 公积金（个人）
        if (newEmployee.getProvidentFundPersonal() != null) {
            existing.setProvidentFundPersonal(newEmployee.getProvidentFundPersonal());
        }
        // 社保信息
        if (newEmployee.getSocialInsurance() != null) {
            existing.setSocialInsurance(newEmployee.getSocialInsurance());
        }
    }



    /**
     * 根据交付单编号和业务类型查找现有员工记录（如果有多个取ID最大的那个）
     * 通用的查找方法，子类可以直接使用
     *
     * @param employee 员工信息
     * @param bizType 业务类型
     * @return 现有的员工记录，如果不存在则返回null
     */
    protected ValueAddedEmployee findExistingEmployeeByDeliveryOrderAndBizType(ValueAddedEmployee employee, Integer bizType) {
        return valueAddedEmployeeService.getOneEmployeeByDeliveryOrderAndBizType(
                employee.getDeliveryOrderNo(),
                bizType
        );
    }

}
