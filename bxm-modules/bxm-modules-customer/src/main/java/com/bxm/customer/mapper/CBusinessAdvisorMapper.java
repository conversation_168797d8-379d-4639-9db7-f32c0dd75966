package com.bxm.customer.mapper;

import java.util.List;

import com.bxm.customer.domain.CBusinessAdvisor;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * 顾问Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-08
 */
@Mapper
public interface CBusinessAdvisorMapper extends BaseMapper<CBusinessAdvisor>
{
    /**
     * 查询顾问
     * 
     * @param id 顾问主键
     * @return 顾问
     */
    public CBusinessAdvisor selectCBusinessAdvisorById(Long id);

    /**
     * 查询顾问列表
     * 
     * @param cBusinessAdvisor 顾问
     * @return 顾问集合
     */
    public List<CBusinessAdvisor> selectCBusinessAdvisorList(CBusinessAdvisor cBusinessAdvisor);

    /**
     * 新增顾问
     * 
     * @param cBusinessAdvisor 顾问
     * @return 结果
     */
    public int insertCBusinessAdvisor(CBusinessAdvisor cBusinessAdvisor);

    /**
     * 修改顾问
     * 
     * @param cBusinessAdvisor 顾问
     * @return 结果
     */
    public int updateCBusinessAdvisor(CBusinessAdvisor cBusinessAdvisor);

    /**
     * 删除顾问
     * 
     * @param id 顾问主键
     * @return 结果
     */
    public int deleteCBusinessAdvisorById(Long id);

    /**
     * 批量删除顾问
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCBusinessAdvisorByIds(Long[] ids);
}
