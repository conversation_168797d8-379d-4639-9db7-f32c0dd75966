package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerOtherInfo;

/**
 * 新户流转其他信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerOtherInfoMapper extends BaseMapper<NewCustomerOtherInfo>
{
    /**
     * 查询新户流转其他信息
     * 
     * @param id 新户流转其他信息主键
     * @return 新户流转其他信息
     */
    public NewCustomerOtherInfo selectNewCustomerOtherInfoById(Long id);

    /**
     * 查询新户流转其他信息列表
     * 
     * @param newCustomerOtherInfo 新户流转其他信息
     * @return 新户流转其他信息集合
     */
    public List<NewCustomerOtherInfo> selectNewCustomerOtherInfoList(NewCustomerOtherInfo newCustomerOtherInfo);

    /**
     * 新增新户流转其他信息
     * 
     * @param newCustomerOtherInfo 新户流转其他信息
     * @return 结果
     */
    public int insertNewCustomerOtherInfo(NewCustomerOtherInfo newCustomerOtherInfo);

    /**
     * 修改新户流转其他信息
     * 
     * @param newCustomerOtherInfo 新户流转其他信息
     * @return 结果
     */
    public int updateNewCustomerOtherInfo(NewCustomerOtherInfo newCustomerOtherInfo);

    /**
     * 删除新户流转其他信息
     * 
     * @param id 新户流转其他信息主键
     * @return 结果
     */
    public int deleteNewCustomerOtherInfoById(Long id);

    /**
     * 批量删除新户流转其他信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerOtherInfoByIds(Long[] ids);
}
