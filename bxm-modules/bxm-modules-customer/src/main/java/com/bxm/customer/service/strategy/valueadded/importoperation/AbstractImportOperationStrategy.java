package com.bxm.customer.service.strategy.valueadded.importoperation;

import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.customer.domain.dto.valueAdded.BaseImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.ImportValidationErrorDTO;
import com.bxm.customer.domain.dto.valueAdded.ImportValidationResult;
import com.bxm.customer.domain.dto.valueAdded.TemplateParseResult;
import com.bxm.customer.helper.ValueAddedImportValidationHelper;
import com.bxm.customer.service.strategy.ImportOperationStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 导入操作策略抽象基类
 *
 * 提供通用的Excel解析逻辑和基础校验功能，子类只需实现特定的业务校验逻辑
 * 实现了职责分离：
 * 1. parseExcelFile - 纯文件解析
 * 2. validateData - 纯数据校验
 * 3. parseTemplateFile - 组合调用（向后兼容）
 *
 * <AUTHOR>
 * @date 2025-08-27
 */
@Slf4j
public abstract class AbstractImportOperationStrategy implements ImportOperationStrategy {

    @Autowired
    protected ValueAddedImportValidationHelper valueAddedImportValidationHelper;

    /**
     * 获取对应的Excel DTO类型
     * 子类需要实现此方法返回对应的DTO类型
     *
     * @return Excel DTO类型
     */
    protected abstract Class<? extends BaseImportExcelDTO> getExcelDtoClass();

    /**
     * 执行特定业务校验
     * 子类可以重写此方法实现特定的业务校验逻辑
     *
     * @param validData 通过基础校验的数据列表
     * @return 特定业务校验结果
     */
    protected TemplateParseResult validateSpecificData(List<? extends BaseImportExcelDTO> validData) {
        return TemplateParseResult.success(validData);
    }

    @Override
    public List<? extends BaseImportExcelDTO> parseExcelFile(InputStream templateStream) throws Exception {
        String operationType = getSupportedOperationType().name();

        try {
            // 使用ExcelUtil解析Excel文件为对应的DTO列表
            Class<? extends BaseImportExcelDTO> dtoClass = getExcelDtoClass();
            ExcelUtil<? extends BaseImportExcelDTO> excelUtil = new ExcelUtil<>(dtoClass);
            List<? extends BaseImportExcelDTO> dataList = excelUtil.importExcel(templateStream);

            // 设置行号用于错误定位
            for (int i = 0; i < dataList.size(); i++) {
                dataList.get(i).setRowNumber(i + 2); // Excel从第2行开始是数据行
            }

            log.info("Parse {} operation Excel file completed, total rows: {}", operationType, dataList.size());
            return dataList;

        } catch (Exception e) {
            log.error("{} operation Excel file parsing failed: {}", operationType, e.getMessage(), e);
            throw new Exception(operationType + " operation Excel file parsing failed: " + e.getMessage(), e);
        }
    }

    @Override
    public TemplateParseResult validateData(List<? extends BaseImportExcelDTO> dataList) {
        String operationType = getSupportedOperationType().name();
        log.info("Start validating {} operation data, total rows: {}", operationType, dataList.size());
        // 第一阶段：执行基础校验
        ImportValidationResult basicValidationResult = valueAddedImportValidationHelper.performBasicValidation(dataList);
        if (!basicValidationResult.getIsValid()) {
            // 基础校验失败，返回错误结果
            log.error("{} operation basic validation failed, error count: {}", operationType, basicValidationResult.getErrors().size());
            return TemplateParseResult.failure(basicValidationResult.getErrors());
        }

        // 第二阶段：基础校验通过，执行业务校验
        ImportValidationResult businessValidationResult = valueAddedImportValidationHelper.performBusinessValidation(
                basicValidationResult.getValidData(), operationType);

        // 合并基础校验和业务校验的错误
        List<ImportValidationErrorDTO> allErrors = new ArrayList<>();
        if (basicValidationResult.getErrors() != null) {
            allErrors.addAll(basicValidationResult.getErrors());
        }
        if (businessValidationResult.getErrors() != null) {
            allErrors.addAll(businessValidationResult.getErrors());
        }

        // 如果业务校验完全失败（没有有效数据），返回错误结果
        if (!businessValidationResult.getIsValid() ||
            businessValidationResult.getValidData() == null ||
            businessValidationResult.getValidData().isEmpty()) {
            log.error("{} operation business validation failed, error count: {}", operationType, businessValidationResult.getErrors().size());
            return TemplateParseResult.failure(allErrors);
        }

        // 第三阶段：业务校验通过，执行特定业务校验
        TemplateParseResult specificValidationResult = validateSpecificData(businessValidationResult.getValidData());

        // 整合所有校验结果
        // 合并特定业务校验的错误（如果有）
        if (specificValidationResult.getHasValidationErrors()) {
            allErrors.addAll(specificValidationResult.getValidationErrors());
        }

        // 获取最终的成功数据和状态
        List<? extends BaseImportExcelDTO> finalSuccessData = specificValidationResult.getSuccessData();
        boolean hasSuccessData = finalSuccessData != null && !finalSuccessData.isEmpty();
        boolean hasAnyErrors = !allErrors.isEmpty();

        // 情况1：没有成功数据，返回完全失败结果
        if (!hasSuccessData) {
            log.error("{} operation validation failed, no valid data, total errors: {}", operationType, allErrors.size());
            return TemplateParseResult.failure(allErrors);
        }
        // 情况2：有成功数据但也有错误，返回部分成功结果
        if (hasAnyErrors) {
            log.warn("{} operation validation completed with errors, success: {}, errors: {}",
                    operationType, finalSuccessData.size(), allErrors.size());
            return TemplateParseResult.partial(finalSuccessData, allErrors);
        }
        // 情况3：所有校验都通过，返回完全成功结果
        log.info("{} operation validation completed successfully, total: {}", operationType, finalSuccessData.size());
        return specificValidationResult;
    }
}
