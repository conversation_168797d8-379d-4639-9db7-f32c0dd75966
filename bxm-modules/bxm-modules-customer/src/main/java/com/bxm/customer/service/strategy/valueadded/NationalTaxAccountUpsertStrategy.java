package com.bxm.customer.service.strategy.valueadded;

import com.alibaba.fastjson2.JSON;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.service.strategy.AbstractEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 国税账号业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class NationalTaxAccountUpsertStrategy extends AbstractEmployeeUpsertStrategy {

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.NATIONAL_TAX_ACCOUNT.getCode();
    }

    @Override
    protected void validateBusinessFields(ValueAddedEmployeeVO employeeVO) {
        ValueAddedOperationType operationType = ValueAddedOperationType.getByCode(ValueAddedBizType.NATIONAL_TAX_ACCOUNT, employeeVO.getOperationType());
        if (operationType == ValueAddedOperationType.REMOTE_REAL_NAME) {
            return;
        }
        if (employeeVO.getOperationType() == null) {
            throw new IllegalArgumentException("国税账号的操作方式不能为空");
        }
        if (StringUtils.isEmpty(employeeVO.getTaxNumber())) {
            throw new IllegalArgumentException("国税账号的账号不能为空");
        }

        if (StringUtils.isEmpty(employeeVO.getQueryPassword())) {
            throw new IllegalArgumentException("国税账号的登录密码不能为空");
        }
    }

    @Override
    protected void preprocessEmployee(ValueAddedEmployee employee) {
        // 标准化税号（去除空格，转大写）
        if (StringUtils.isNotEmpty(employee.getTaxNumber())) {
            employee.setTaxNumber(employee.getTaxNumber().trim().toUpperCase());
        }

        // 构建扩展信息
        ValueAddedOperationType operationType = getOperationTypeByCode(employee.getOperationType());
        if (operationType != null) {
            Map<String, Object> bizTypeExtendInfo = buildBizTypeExtendInfo(
                    ValueAddedBizType.NATIONAL_TAX_ACCOUNT,
                    operationType
            );
            employee.setExtendInfo(mergeExtendInfo(employee.getExtendInfo(), bizTypeExtendInfo));
        }

    }



    @Override
    protected ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 国税账号业务的唯一性判断：交付单编号 + 业务类型
        return findExistingEmployeeByDeliveryOrderAndBizType(employee, ValueAddedBizType.NATIONAL_TAX_ACCOUNT.getCode());
    }

    @Override
    protected ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 先调用父类的通用合并逻辑
        super.mergeEmployee(existing, newEmployee);

        // 国税账号特定字段的非空值复制
        if (StringUtils.isNotEmpty(newEmployee.getTaxNumber())) {
            existing.setTaxNumber(newEmployee.getTaxNumber());
        }
        if (StringUtils.isNotEmpty(newEmployee.getQueryPassword())) {
            existing.setQueryPassword(newEmployee.getQueryPassword());
        }
        if (newEmployee.getLoginMethod() != null) {
            existing.setLoginMethod(newEmployee.getLoginMethod());
        }

        log.info("National tax account employee merged: ID={}, Name={}, TaxNumber={}",
                existing.getId(), existing.getEmployeeName(), maskSensitiveInfo(existing.getTaxNumber()));

        return existing;
    }

    /**
     * 构建业务类型的扩展信息
     *
     * @param bizType 业务类型枚举
     * @param operationType 操作类型枚举
     * @return 扩展信息Map
     */
    private Map<String, Object> buildBizTypeExtendInfo(ValueAddedBizType bizType, ValueAddedOperationType operationType) {
        Map<String, Object> extendInfo = new HashMap<>();
        extendInfo.put("accountType", bizType.getName());
        extendInfo.put("businessType", operationType.getName());
        return extendInfo;
    }

    /**
     * 合并扩展信息到现有的扩展信息中
     *
     * @param existingExtendInfo 现有的扩展信息JSON字符串
     * @param newExtendInfo 新的扩展信息Map
     * @return 合并后的扩展信息JSON字符串
     */
    private String mergeExtendInfo(String existingExtendInfo, Map<String, Object> newExtendInfo) {
        Map<String, Object> extendInfo = parseExtendInfo(existingExtendInfo);
        extendInfo.putAll(newExtendInfo);
        return JSON.toJSONString(extendInfo);
    }

    /**
     * 解析扩展信息JSON字符串为Map
     *
     * @param extendInfoJson 扩展信息JSON字符串
     * @return 扩展信息Map
     */
    private Map<String, Object> parseExtendInfo(String extendInfoJson) {
        if (StringUtils.isEmpty(extendInfoJson)) {
            return new HashMap<>();
        }
        try {
            return JSON.parseObject(extendInfoJson, Map.class);
        } catch (Exception e) {
            log.warn("Failed to parse extend info JSON: {}, using empty map", extendInfoJson);
            return new HashMap<>();
        }
    }

    /**
     * 根据操作类型代码获取对应的枚举
     *
     * @param operationTypeCode 操作类型代码
     * @return 操作类型枚举
     */
    private ValueAddedOperationType getOperationTypeByCode(Integer operationTypeCode) {
        if (operationTypeCode == null) {
            return null;
        }

        switch (operationTypeCode) {
            case 1:
                return ValueAddedOperationType.ACCOUNTING_REAL_NAME;
            case 2:
                return ValueAddedOperationType.REMOTE_REAL_NAME;
            default:
                return null;
        }
    }

    /**
     * 根据操作类型代码获取中文名称
     *
     * @param operationType 操作类型代码
     * @return 操作类型中文名称
     */
    private String getOperationTypeName(Integer operationType) {
        ValueAddedOperationType operationTypeEnum = getOperationTypeByCode(operationType);
        if (operationTypeEnum != null) {
            return operationTypeEnum.getName();
        }

        return operationType != null ? "未知操作(" + operationType + ")" : "未知操作";
    }

    /**
     * 脱敏敏感信息用于日志输出
     *
     * @param sensitiveInfo 敏感信息
     * @return 脱敏后的信息
     */
    private String maskSensitiveInfo(String sensitiveInfo) {
        if (StringUtils.isEmpty(sensitiveInfo)) {
            return sensitiveInfo;
        }

        if (sensitiveInfo.length() <= 4) {
            return "****";
        }

        // 保留前2位和后2位，中间用*替代
        return sensitiveInfo.substring(0, 2) + "****" + sensitiveInfo.substring(sensitiveInfo.length() - 2);
    }

}
