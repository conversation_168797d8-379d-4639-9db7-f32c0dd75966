package com.bxm.customer.validation;

import com.bxm.customer.domain.enums.ValueAddedOperTypeEnum;

import javax.validation.Constraint;
import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import javax.validation.Payload;
import java.lang.annotation.*;

/**
 * 操作类型验证注解
 * 
 * 验证操作类型代码是否在ValueAddedOperTypeEnum中定义
 *
 * <AUTHOR>
 * @date 2025-09-01
 */
@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidOperType.ValidOperTypeValidator.class)
@Documented
public @interface ValidOperType {

    String message() default "无效的操作类型";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    /**
     * 是否允许空值
     */
    boolean allowNull() default true;

    /**
     * 操作类型验证器
     */
    class ValidOperTypeValidator implements ConstraintValidator<ValidOperType, String> {

        private boolean allowNull;

        @Override
        public void initialize(ValidOperType constraintAnnotation) {
            this.allowNull = constraintAnnotation.allowNull();
        }

        @Override
        public boolean isValid(String value, ConstraintValidatorContext context) {
            // 如果值为空
            if (value == null || value.trim().isEmpty()) {
                return allowNull;
            }

            // 验证操作类型是否有效
            return ValueAddedOperTypeEnum.isValid(value.trim());
        }
    }
}