package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.WorkOrder;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.workOrder.WorkOrderDTO;
import com.bxm.customer.domain.vo.workOrder.WorkOrderSearchVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2024-05-07
 */
@Mapper
public interface WorkOrderMapper extends BaseMapper<WorkOrder>
{

    List<WorkOrderDTO> workOrderList(IPage<WorkOrderDTO> result,
                                     @Param("vo") WorkOrderSearchVO vo,
                                     @Param("userDept") UserDeptDTO userDept);

    Long workOrderCountStatistic(@Param("userDept") UserDeptDTO userDeptDTO, @Param("type") Integer type);

    List<CommonDeptCountDTO> workOrderInitiateDeptList(@Param("userDept") UserDeptDTO userDept,
                                                       @Param("tabType") Integer tabType);

    List<CommonDeptCountDTO> workOrderUndertakeDeptList(@Param("userDept") UserDeptDTO userDept,
                                                        @Param("tabType") Integer tabType);

    List<WorkOrderDTO> workOrderListForXm(@Param("vo") WorkOrderSearchVO vo, @Param("deptIds") List<Long> deptIds);
}
