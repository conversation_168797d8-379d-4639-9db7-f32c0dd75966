package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 交付异常关闭状态变更策略
 *
 * 处理从"交付异常(待确认)"状态到"交付关闭"状态的转换变更
 *
 * 支持的状态转换：
 * DELIVERY_EXCEPTION -> DELIVERY_CLOSED (关闭交付)
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class DeliveryExceptionStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public ValueAddedDeliveryOrderStatus getSupport() {
        return ValueAddedDeliveryOrderStatus.DELIVERY_EXCEPTION;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedTargetStatuses() {
        // 交付异常状态可以转换到：待交付、关闭交付
        return Arrays.asList(
            ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY,  // 正向：重新进入交付流程
            ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED              // 正向：关闭交付
        );
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        // 只处理 DELIVERY_CLOSED 目标状态
        if (targetStatus == ValueAddedDeliveryOrderStatus.DELIVERY_CLOSED) {
            validateCloseOperation(request, "交付");
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {

        } else {
            throwUnsupportedTransition("交付异常", request.getTargetStatus());
        }
    }


}
