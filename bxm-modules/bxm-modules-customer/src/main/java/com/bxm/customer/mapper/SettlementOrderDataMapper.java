package com.bxm.customer.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataCountDTO;
import com.bxm.customer.domain.dto.settlementOrder.SettlementOrderDataDTO;
import com.bxm.customer.domain.vo.settlementOrder.SettlementOrderDataSearchVO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.SettlementOrderData;
import org.apache.ibatis.annotations.Param;

/**
 * 结算单关联数据Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Mapper
public interface SettlementOrderDataMapper extends BaseMapper<SettlementOrderData>
{
    /**
     * 查询结算单关联数据
     * 
     * @param id 结算单关联数据主键
     * @return 结算单关联数据
     */
    public SettlementOrderData selectSettlementOrderDataById(Long id);

    /**
     * 查询结算单关联数据列表
     * 
     * @param settlementOrderData 结算单关联数据
     * @return 结算单关联数据集合
     */
    public List<SettlementOrderData> selectSettlementOrderDataList(SettlementOrderData settlementOrderData);

    /**
     * 新增结算单关联数据
     * 
     * @param settlementOrderData 结算单关联数据
     * @return 结果
     */
    public int insertSettlementOrderData(SettlementOrderData settlementOrderData);

    /**
     * 修改结算单关联数据
     * 
     * @param settlementOrderData 结算单关联数据
     * @return 结果
     */
    public int updateSettlementOrderData(SettlementOrderData settlementOrderData);

    /**
     * 删除结算单关联数据
     * 
     * @param id 结算单关联数据主键
     * @return 结果
     */
    public int deleteSettlementOrderDataById(Long id);

    /**
     * 批量删除结算单关联数据
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSettlementOrderDataByIds(Long[] ids);

    void saveBySettlementOrderDataTemp(@Param("batchNo") String batchNo,
                                       @Param("businessDeptId") Long businessDeptId,
                                       @Param("settlementOrderId") Long settlementOrderId,
                                       @Param("settlementType") Integer settlementType,
                                       @Param("isSupplement") Boolean isSupplement);

    List<SettlementOrderDataDTO> settlementOrderDataListBySettlementOrderId(IPage<SettlementOrderDataDTO> iPage, @Param("vo") SettlementOrderDataSearchVO vo);

    List<SettlementOrderDataDTO> settlementOrderDataListBySettlementOrderIds(@Param("settlementOrderIds") List<Long> settlementOrderIds);

    void saveByPeriodIds(@Param("periodMonthIds") List<Long> periodMonthIds,
                         @Param("settlementOrderId") Long settlementOrderId);

    void saveByCustomerIds(@Param("customerServiceIds") List<Long> customerServiceIds,
                           @Param("settlementOrderId") Long settlementOrderId);

    List<SettlementOrderDataCountDTO> selectBatchSettlementOrderDataCount(@Param("settlementOrderIds") List<Long> settlementOrderIds);

    void updateNewUserPrepaymentDataSettlementStatus(@Param("settlementOrderIds") List<Long> settlementOrderIds, @Param("newSettlementStatus") Integer newSettlementStatus);

    void updateInAccountDataSettlementStatus(@Param("settlementOrderIds") List<Long> settlementOrderIds, @Param("newSettlementStatus") Integer newSettlementStatus);

    void updateInAccountDataPrepayStatus(@Param("settlementOrderIds") List<Long> settlementOrderIds, @Param("newPrepayStatus") Integer newPrepayStatus);
}
