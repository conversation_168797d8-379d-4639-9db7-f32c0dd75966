package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerMattersNotes;

/**
 * 事项备忘Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-19
 */
@Mapper
public interface CustomerMattersNotesMapper extends BaseMapper<CustomerMattersNotes>
{
    /**
     * 查询事项备忘
     * 
     * @param id 事项备忘主键
     * @return 事项备忘
     */
    public CustomerMattersNotes selectCustomerMattersNotesById(Long id);

    /**
     * 查询事项备忘列表
     * 
     * @param customerMattersNotes 事项备忘
     * @return 事项备忘集合
     */
    public List<CustomerMattersNotes> selectCustomerMattersNotesList(CustomerMattersNotes customerMattersNotes);

    /**
     * 新增事项备忘
     * 
     * @param customerMattersNotes 事项备忘
     * @return 结果
     */
    public int insertCustomerMattersNotes(CustomerMattersNotes customerMattersNotes);

    /**
     * 修改事项备忘
     * 
     * @param customerMattersNotes 事项备忘
     * @return 结果
     */
    public int updateCustomerMattersNotes(CustomerMattersNotes customerMattersNotes);

    /**
     * 删除事项备忘
     * 
     * @param id 事项备忘主键
     * @return 结果
     */
    public int deleteCustomerMattersNotesById(Long id);

    /**
     * 批量删除事项备忘
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerMattersNotesByIds(Long[] ids);
}
