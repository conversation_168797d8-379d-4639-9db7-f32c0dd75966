package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerDeliverFile;

/**
 * 交付附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
@Mapper
public interface CustomerDeliverFileMapper extends BaseMapper<CustomerDeliverFile>
{
    /**
     * 查询交付附件
     * 
     * @param id 交付附件主键
     * @return 交付附件
     */
    public CustomerDeliverFile selectCustomerDeliverFileById(Long id);

    /**
     * 查询交付附件列表
     * 
     * @param customerDeliverFile 交付附件
     * @return 交付附件集合
     */
    public List<CustomerDeliverFile> selectCustomerDeliverFileList(CustomerDeliverFile customerDeliverFile);

    /**
     * 新增交付附件
     * 
     * @param customerDeliverFile 交付附件
     * @return 结果
     */
    public int insertCustomerDeliverFile(CustomerDeliverFile customerDeliverFile);

    /**
     * 修改交付附件
     * 
     * @param customerDeliverFile 交付附件
     * @return 结果
     */
    public int updateCustomerDeliverFile(CustomerDeliverFile customerDeliverFile);

    /**
     * 删除交付附件
     * 
     * @param id 交付附件主键
     * @return 结果
     */
    public int deleteCustomerDeliverFileById(Long id);

    /**
     * 批量删除交付附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerDeliverFileByIds(Long[] ids);
}
