package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.domain.enums.TaxpayerTypeEnum;
import com.bxm.customer.domain.enums.AccountingMethodEnum;
import lombok.extern.slf4j.Slf4j;

/**
 * 状态变更策略抽象基类
 *
 * 提供所有状态变更策略的公共验证逻辑和模板方法
 * 使用模板方法模式减少代码重复，提高代码可维护性
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Slf4j
public abstract class ValueAddedAbstractStatusChangeStrategy implements StatusChangeStrategy {

    @Override
    public final boolean supports(ValueAddedDeliveryOrderStatus currentStatus) {
        return currentStatus == getSupport();
    }

    @Override
    public final boolean isValidTransition(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus) {
        // 1. 检查当前策略是否支持此当前状态
        // 2. 检查目标状态是否在允许的目标状态列表中
        return supports(currentStatus) && getAllowedTargetStatuses().contains(targetStatus);
    }

    @Override
    public final void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request) {
        ValueAddedDeliveryOrderStatus currentStatus = ValueAddedDeliveryOrderStatus.getByCode(order.getStatus());
        ValueAddedDeliveryOrderStatus targetStatus = getTargetStatusEnum(request);

        // 验证状态转换是否合法
        if (!isValidTransition(currentStatus, targetStatus)) {
            throw new IllegalArgumentException(
                String.format("不支持从状态 %s 转换到状态 %s",  currentStatus.getDescription(), targetStatus.getDescription()));
        }

        //validateSpecificTransition(order, request, targetStatus);
    }

    /**
     * 获取当前策略支持的当前状态
     * 子类必须实现此方法来指定策略支持的当前状态
     *
     * @return 支持的当前状态
     */
    public abstract ValueAddedDeliveryOrderStatus getSupport();

    /**
     * 子类实现具体的状态转换验证逻辑
     *
     * @param order 增值交付单实体
     * @param request 状态变更请求
     * @param targetStatus 目标状态枚举
     */
    protected abstract void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                                     StatusChangeRequestDTO request,
                                                     ValueAddedDeliveryOrderStatus targetStatus);



    /**
     * 验证备注字段
     *
     * @param remark 备注内容
     * @param context 验证上下文，用于错误信息
     * @throws IllegalArgumentException 当备注为空时抛出
     */
    protected void validateRemark(String remark, String context) {
        validateNotEmpty(remark, context + "备注信息");
    }

    /**
     * 验证字段非空
     *
     * @param value 待验证的值
     * @param fieldName 字段名称，用于错误信息
     * @throws IllegalArgumentException 当字段为空时抛出
     */
    protected void validateNotEmpty(String value, String fieldName) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException(fieldName + "不能为空");
        }
    }

    /**
     * 验证客户基本信息
     *
     * @param order 增值交付单实体
     * @throws IllegalArgumentException 当客户信息不完整时抛出
     */
    protected void validateCustomerInfo(ValueAddedDeliveryOrder order) {
        if (order.getCustomerId() == null) {
            throw new IllegalArgumentException("客户ID不能为空");
        }
    }

    /**
     * 验证联络人信息
     *
     * @param order 增值交付单实体
     * @throws IllegalArgumentException 当联络人信息不完整时抛出
     */
    protected void validateContactInfo(ValueAddedDeliveryOrder order) {
        validateNotEmpty(order.getContactMobile(), "联络人手机号");
    }

    /**
     * 验证纳税人类型
     *
     * @param order 增值交付单实体
     * @throws IllegalArgumentException 当纳税人类型无效时抛出
     */
    protected void validateTaxpayerType(ValueAddedDeliveryOrder order) {
        if (order.getTaxpayerType() == null || !TaxpayerTypeEnum.isValid(order.getTaxpayerType())) {
            throw new IllegalArgumentException("纳税人类型必须为1（小规模纳税人）或2（一般纳税人）");
        }
    }

    /**
     * 验证统一社会信用代码
     *
     * @param order 增值交付单实体
     * @throws IllegalArgumentException 当信用代码为空时抛出
     */
    protected void validateCreditCode(ValueAddedDeliveryOrder order) {
        validateNotEmpty(order.getCreditCode(), "统一社会信用代码");
    }

    /**
     * 验证入账方式
     *
     * @param request 状态变更请求
     * @throws IllegalArgumentException 当入账方式无效时抛出
     */
    protected void validateAccountingMethod(StatusChangeRequestDTO request) {
        if (request.getAccountingMethod() != null && !AccountingMethodEnum.isValid(request.getAccountingMethod())) {
            throw new IllegalArgumentException("无效的入账方式: " + request.getAccountingMethod());
        }
    }



    /**
     * 获取目标状态枚举
     *
     * @param request 状态变更请求
     * @return 目标状态枚举
     */
    protected ValueAddedDeliveryOrderStatus getTargetStatusEnum(StatusChangeRequestDTO request) {
        return ValueAddedDeliveryOrderStatus.getByCode(request.getTargetStatus());
    }

    /**
     * 抛出不支持的状态转换异常
     *
     * @param currentStatus 当前状态描述
     * @param targetStatus 目标状态代码
     * @throws IllegalArgumentException 总是抛出此异常
     */
    protected void throwUnsupportedTransition(String currentStatus, String targetStatus) {
        throw new IllegalArgumentException("不支持从" + currentStatus + "状态转换到: " + targetStatus);
    }

    /**
     * 验证常见的退回操作
     */
    protected void validateReturnOperation(StatusChangeRequestDTO request, String context) {
        // 退回操作基础验证，子类可根据需要扩展
    }

    /**
     * 验证常见的关闭操作
     *
     * @param request 状态变更请求
     * @param context 操作上下文
     */
    protected void validateCloseOperation(StatusChangeRequestDTO request, String context) {
        validateRemark(request.getRemark(), "关闭" + context);
    }
}
