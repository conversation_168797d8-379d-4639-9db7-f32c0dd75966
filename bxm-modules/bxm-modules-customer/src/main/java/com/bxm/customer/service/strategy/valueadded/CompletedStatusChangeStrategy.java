package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 已完成状态变更策略
 *
 * 处理从"已完成"状态到"待确认"状态的转换变更
 *
 * 支持的状态转换：
 * COMPLETED -> PENDING_CONFIRMATION (待确认)
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class CompletedStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public ValueAddedDeliveryOrderStatus getSupport() {
        return ValueAddedDeliveryOrderStatus.COMPLETED;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedTargetStatuses() {
        // 已完成状态可以转换到：待确认(逆向)
        return Arrays.asList(
            ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION  // 逆向：退回到待确认
        );
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        if (targetStatus == ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION) {

        } else {
            throwUnsupportedTransition("已完成", request.getTargetStatus());
        }
    }


}
