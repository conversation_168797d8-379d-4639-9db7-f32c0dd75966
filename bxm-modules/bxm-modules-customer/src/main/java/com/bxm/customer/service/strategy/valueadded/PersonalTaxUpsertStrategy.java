package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.helper.ValueAddedEmpValidationHelper;
import com.bxm.customer.service.strategy.AbstractEmployeeUpsertStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 个税明细业务类型的upsert策略实现
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Component
public class PersonalTaxUpsertStrategy extends AbstractEmployeeUpsertStrategy {

    @Override
    public Integer getSupportedBizType() {
        return ValueAddedBizType.PERSONAL_TAX.getCode();
    }

    @Override
    protected void validateBusinessFields(ValueAddedEmployeeVO employeeVO) {
        // 1. 执行通用业务字段校验
        validateCommonBusinessFields(employeeVO);

        // 2. 个税明细业务特有校验
        if (ValueAddedEmpValidationHelper.isReductionOperation(employeeVO.getOperationType())) {
            // 3. 减员操作特殊校验：检查身份证和手机号是否已存在
            validateReductionExistence(employeeVO);
        } else {
            // 验证应发工资（个税明细业务必须有工资信息）
            if (employeeVO.getGrossSalary() == null || employeeVO.getGrossSalary().doubleValue() <= 0) {
                throw new IllegalArgumentException("个税明细业务应发工资不能为空且必须为正数");
            }
        }
    }

    /**
     * 校验减员操作时的身份证和手机号存在性
     *
     * @param employeeVO 员工信息VO
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    private void validateReductionExistence(ValueAddedEmployeeVO employeeVO) {
        String deliveryOrderNo = employeeVO.getDeliveryOrderNo();
        String voIdNumber = employeeVO.getIdNumber();
        String voMobile = employeeVO.getMobile();
        Integer bizType = getSupportedBizType();

        // 分别检查身份证和手机号是否存在
        ValueAddedEmployee existingByIdNumber = valueAddedEmployeeService.getByDeliveryOrderAndIdNumber(
                deliveryOrderNo, voIdNumber, bizType);
        ValueAddedEmployee existingByMobile = valueAddedEmployeeService.getByDeliveryOrderAndMobile(
                deliveryOrderNo, voMobile, bizType);

        // 根据匹配情况给出具体的错误提示
        if (existingByIdNumber != null && existingByMobile != null) {
            // 身份证和手机号都匹配（同一条记录）
            throw new IllegalArgumentException("同一交付单内身份证相同且手机号相同不允许提交");
        } else if (existingByIdNumber != null) {
            // 仅身份证匹配
            throw new IllegalArgumentException("同一交付单内身份证相同不允许提交");
        } else if (existingByMobile != null) {
            // 仅手机号匹配
            throw new IllegalArgumentException("同一交付单内手机号相同不允许提交");
        }
    }

    @Override
    protected void preprocessEmployee(ValueAddedEmployee employee) {
        // 执行通用预处理（包含身份证号标准化等）
        super.preprocessEmployee(employee);
    }

    @Override
    protected ValueAddedEmployee findExistingEmployee(ValueAddedEmployee employee) {
        // 个税明细业务的唯一性判断：交付单编号 + 身份证号 + 业务类型
        return findExistingEmployeeByIdNumber(employee, ValueAddedBizType.PERSONAL_TAX.getCode());
    }

    @Override
    protected ValueAddedEmployee mergeEmployee(ValueAddedEmployee existing, ValueAddedEmployee newEmployee) {
        // 调用父类的通用合并逻辑（已包含个税明细需要的所有字段合并）
        return super.mergeEmployee(existing, newEmployee);
    }


}
