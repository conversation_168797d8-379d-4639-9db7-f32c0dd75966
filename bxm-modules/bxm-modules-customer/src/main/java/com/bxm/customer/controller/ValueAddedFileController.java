package com.bxm.customer.controller;

import com.bxm.common.core.web.controller.BaseController;
import com.bxm.common.core.domain.Result;
import com.bxm.common.log.annotation.Log;
import com.bxm.common.log.enums.BusinessType;
import com.bxm.customer.domain.enums.ValueAddedFileType;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedStockVO;
import com.bxm.customer.domain.vo.valueAdded.StockUploadResultVO;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.bxm.file.api.domain.ValueAddedFileDTO;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 增值交付单文件Controller
 *
 * 提供增值交付单相关文件的上传和管理功能
 *
 * <AUTHOR>
 * @date 2025-08-02
 */
@Slf4j
@RestController
@RequestMapping("/valueAddedFile")
@Api(tags = "增值交付单文件管理")
public class ValueAddedFileController extends BaseController {

    @Autowired
    private IValueAddedFileService valueAddedFileService;

    /**
     * 上传交付单文件
     */
    @PostMapping("/uploadDeliveryOrderFile")
    @ApiOperation(value = "上传交付单文件", notes = "上传与指定交付单编号关联的文件")
    @Log(title = "上传交付单文件", businessType = BusinessType.INSERT)
    public Result<Long> uploadDeliveryOrderFile(
            @RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo,
            @RequestParam("file") @ApiParam(value = "要上传的文件", required = true) MultipartFile file) {
        try {
            log.info("Received upload delivery order file request, deliveryOrderNo: {}, fileName: {}", deliveryOrderNo, file != null ? file.getOriginalFilename() : "null");

            // 基础参数验证
            if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
                return Result.fail("交付单编号不能为空");
            }
            if (file == null || file.isEmpty()) {
                return Result.fail("文件不能为空");
            }

            // 调用服务层保存文件
            Long fileId = valueAddedFileService.saveDeliveryOrderFile(file, deliveryOrderNo);

            log.info("Upload delivery order file success, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);
            return Result.ok(fileId, "文件上传成功");

        } catch (IllegalArgumentException e) {
            log.warn("Parameter validation error in upload delivery order file: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in upload delivery order file, deliveryOrderNo: {}", deliveryOrderNo, e);
            return Result.fail("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 上传库存Excel文件
     */
    @PostMapping("/uploadStock")
    @ApiOperation(value = "上传库存Excel文件", notes = "上传与指定交付单编号关联的库存Excel文件，解析各Sheet中的库存数据")
    @Log(title = "上传库存Excel文件", businessType = BusinessType.INSERT)
    public Result<StockUploadResultVO> uploadStock(
            @RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo,
            @RequestParam("file") @ApiParam(value = "要上传的库存Excel文件", required = true) MultipartFile file) {
        try {
            log.info("Received upload stock excel file request, deliveryOrderNo: {}, fileName: {}", deliveryOrderNo, file != null ? file.getOriginalFilename() : "null");

            // 基础参数验证
            if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
                return Result.fail("交付单编号不能为空");
            }
            if (file == null || file.isEmpty()) {
                return Result.fail("文件不能为空");
            }

            // 文件格式验证
            String fileName = file.getOriginalFilename();
            if (fileName == null || (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
                return Result.fail("文件格式不正确，请上传Excel文件(.xlsx或.xls)");
            }

            // 调用服务层保存并解析文件
            StockUploadResultVO result = valueAddedFileService.saveStockExcelFile(file, deliveryOrderNo);

            log.info("Upload stock excel file success, stockCount: {}, deliveryOrderNo: {}", 
                    result.getStockList() != null ? result.getStockList().size() : 0, deliveryOrderNo);
            return Result.ok(result, "库存Excel文件上传并解析成功");

        } catch (IllegalArgumentException e) {
            log.warn("Parameter validation error in upload stock excel file: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in upload stock excel file, deliveryOrderNo: {}", deliveryOrderNo, e);
            return Result.fail("库存Excel文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/del")
    @ApiOperation(value = "删除文件", notes = "软删除指定的文件")
    @Log(title = "删除文件", businessType = BusinessType.DELETE)
    public Result<Void> del(
            @RequestParam("fileId") @ApiParam(value = "文件ID", required = true) Long fileId,
            @RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo) {
        try {
            log.info("Received delete file request, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);

            // 基础参数验证
            if (fileId == null) {
                return Result.fail("文件ID不能为空");
            }
            if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
                return Result.fail("交付单编号不能为空");
            }

            // 调用服务层删除文件
            boolean result = valueAddedFileService.deleteFile(fileId, deliveryOrderNo);

            if (result) {
                log.info("Delete file success, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);
                return Result.ok(null, "文件删除成功");
            } else {
                return Result.fail("文件删除失败");
            }

        } catch (IllegalArgumentException e) {
            log.warn("Parameter validation error in delete file: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in delete file, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo, e);
            return Result.fail("文件删除失败：" + e.getMessage());
        }
    }

    /**
     * 上传文件
     */
    @PostMapping("/uploadFile")
    @ApiOperation(value = "上传文件", notes = "上传指定类型的文件并返回文件信息，包含fileId")
    @Log(title = "上传文件", businessType = BusinessType.INSERT)
    public Result<ValueAddedFileDTO> uploadFile(
            @RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo,
            @RequestParam("file") @ApiParam(value = "要上传的文件", required = true) MultipartFile file,
            @RequestParam("bizFileType") @ApiParam(value = "业务文件类型", required = true) Integer bizFileType) {
        try {
            log.info("Received upload file request, deliveryOrderNo: {}, fileName: {}, bizFileType: {}",
                    deliveryOrderNo, file != null ? file.getOriginalFilename() : "null", bizFileType);
            // 基础参数验证
            if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
                return Result.fail("交付单编号不能为空");
            }
            if (file == null || file.isEmpty()) {
                return Result.fail("文件不能为空");
            }
            if (bizFileType == null) {
                return Result.fail("业务文件类型不能为空");
            }

            // 验证文件类型是否有效
            ValueAddedFileType fileType = ValueAddedFileType.getByCode(bizFileType);
            if (fileType == null) {
                return Result.fail("无效的业务文件类型: " + bizFileType);
            }

            // 调用service方法上传文件
            ValueAddedFileDTO result = valueAddedFileService.uploadFile(deliveryOrderNo.trim(), file, fileType);

            log.info("Upload file completed successfully, deliveryOrderNo: {}, fileName: {}, fileUrl: {}, fileId: {}",
                    deliveryOrderNo, result.getFileName(), result.getUrl(), result.getFileId());

            return Result.ok(result);

        } catch (IllegalArgumentException e) {
            log.warn("Upload file validation failed: {}", e.getMessage());
            return Result.fail(e.getMessage());
        } catch (Exception e) {
            log.error("Upload file failed, deliveryOrderNo: {}, fileName: {}, error: {}",
                    deliveryOrderNo, file != null ? file.getOriginalFilename() : "null", e.getMessage(), e);
            return Result.fail("上传文件失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件下载链接
     */
    @GetMapping("/download")
    @ApiOperation(value = "获取文件下载链接", notes = "获取指定文件的下载URL")
    public Result<String> download(
            @RequestParam("fileId") @ApiParam(value = "文件ID", required = true) Long fileId,
            @RequestParam("deliveryOrderNo") @ApiParam(value = "交付单编号", required = true) String deliveryOrderNo) {
        try {
            log.info("Received get download URL request, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);

            // 基础参数验证
            if (fileId == null) {
                return Result.fail("文件ID不能为空");
            }
            if (deliveryOrderNo == null || deliveryOrderNo.trim().isEmpty()) {
                return Result.fail("交付单编号不能为空");
            }

            // 调用服务层获取下载URL
            String downloadUrl = valueAddedFileService.getFileDownloadUrl(fileId, deliveryOrderNo);

            log.info("Get download URL success, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo);
            return Result.ok(downloadUrl, "获取下载链接成功");

        } catch (IllegalArgumentException e) {
            log.warn("Parameter validation error in get download URL: {}", e.getMessage());
            return Result.fail("参数验证失败：" + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error in get download URL, fileId: {}, deliveryOrderNo: {}", fileId, deliveryOrderNo, e);
            return Result.fail("获取下载链接失败：" + e.getMessage());
        }
    }
}
