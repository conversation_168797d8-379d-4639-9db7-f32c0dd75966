package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.BusinessDdlRecord;

/**
 * ddl修改记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Mapper
public interface BusinessDdlRecordMapper extends BaseMapper<BusinessDdlRecord>
{
    /**
     * 查询ddl修改记录
     * 
     * @param id ddl修改记录主键
     * @return ddl修改记录
     */
    public BusinessDdlRecord selectBusinessDdlRecordById(Long id);

    /**
     * 查询ddl修改记录列表
     * 
     * @param businessDdlRecord ddl修改记录
     * @return ddl修改记录集合
     */
    public List<BusinessDdlRecord> selectBusinessDdlRecordList(BusinessDdlRecord businessDdlRecord);

    /**
     * 新增ddl修改记录
     * 
     * @param businessDdlRecord ddl修改记录
     * @return 结果
     */
    public int insertBusinessDdlRecord(BusinessDdlRecord businessDdlRecord);

    /**
     * 修改ddl修改记录
     * 
     * @param businessDdlRecord ddl修改记录
     * @return 结果
     */
    public int updateBusinessDdlRecord(BusinessDdlRecord businessDdlRecord);

    /**
     * 删除ddl修改记录
     * 
     * @param id ddl修改记录主键
     * @return 结果
     */
    public int deleteBusinessDdlRecordById(Long id);

    /**
     * 批量删除ddl修改记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBusinessDdlRecordByIds(Long[] ids);
}
