package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.api.domain.vo.RemoteBusinessTaskSearchV2VO;
import com.bxm.customer.domain.BusinessTask;
import com.bxm.customer.domain.CustomerServiceCashierAccounting;
import com.bxm.customer.domain.dto.businessTask.BusinessTaskAccountingDeptCountDTO;
import com.bxm.customer.domain.dto.businessTask.BusinessTaskForManageDTO;
import com.bxm.customer.domain.dto.businessTask.BusinessTaskForMyDTO;
import com.bxm.customer.domain.dto.businessTask.BusinessTaskForPeriodDTO;
import com.bxm.customer.domain.vo.TagSearchVO;
import com.bxm.customer.domain.vo.businessTask.BusinessTaskVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 业务任务Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Mapper
public interface BusinessTaskMapper extends BaseMapper<BusinessTask> {
    /**
     * 查询业务任务
     *
     * @param id 业务任务主键
     * @return 业务任务
     */
    public BusinessTask selectBusinessTaskById(Long id);

    /**
     * 查询业务任务列表
     *
     * @param businessTask 业务任务
     * @return 业务任务集合
     */
    public List<BusinessTask> selectBusinessTaskList(BusinessTask businessTask);

    /**
     * 新增业务任务
     *
     * @param businessTask 业务任务
     * @return 结果
     */
    public int insertBusinessTask(BusinessTask businessTask);

    /**
     * 修改业务任务
     *
     * @param businessTask 业务任务
     * @return 结果
     */
    public int updateBusinessTask(BusinessTask businessTask);

    /**
     * 删除业务任务
     *
     * @param id 业务任务主键
     * @return 结果
     */
    public int deleteBusinessTaskById(Long id);

    /**
     * 批量删除业务任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBusinessTaskByIds(Long[] ids);


    List<BusinessTaskForPeriodDTO> searchListForPeriod(
            IPage<BusinessTaskForPeriodDTO> result,
            @Param("vo") BusinessTaskVO vo,
            @Param("tagSearchVO") TagSearchVO tagSearchVO,
            @Param("customerServiceTagSearchVO") TagSearchVO customerServiceTagSearchVO,
            @Param("userDept") UserDeptDTO userDept,
            @Param("customerServiceIds") List<Long> customerServiceIds
    );

    List<BusinessTaskForManageDTO> searchListForManage(
            IPage<BusinessTaskForManageDTO> result,
            @Param("vo") BusinessTaskVO vo,
            @Param("tagSearchVO") TagSearchVO tagSearchVO,
            @Param("customerServiceTagSearchVO") TagSearchVO customerServiceTagSearchVO,
            @Param("userDept") UserDeptDTO userDept,
            @Param("customerServiceIds") List<Long> customerServiceIds
    );

    List<BusinessTaskForMyDTO> searchListForMy(
            IPage<BusinessTaskForMyDTO> result,
            @Param("vo") BusinessTaskVO vo,
            @Param("tagSearchVO") TagSearchVO tagSearchVO,
            @Param("customerServiceTagSearchVO") TagSearchVO customerServiceTagSearchVO,
            @Param("customerServiceIds") List<Long> customerServiceIds
    );

    List<BusinessTaskAccountingDeptCountDTO> searchListCountForPeriod(@Param("vo") BusinessTaskVO vo,
                                                                      @Param("tagSearchVO") TagSearchVO tagSearchVO,
                                                                      @Param("userDept") UserDeptDTO userDept);

    List<BusinessTaskAccountingDeptCountDTO> searchListCountForManager(@Param("vo") BusinessTaskVO vo,
                                                 @Param("tagSearchVO") TagSearchVO tagSearchVO,
                                                 @Param("userDept") UserDeptDTO userDept);

    List<BusinessTaskAccountingDeptCountDTO> searchListCountForMy(@Param("vo") BusinessTaskVO vo,
                                            @Param("tagSearchVO") TagSearchVO tagSearchVO);

    List<BusinessTaskAccountingDeptCountDTO> searchListBusinessDeptCountForPeriod(@Param("vo") BusinessTaskVO vo,
                                                                                  @Param("tagSearchVO") TagSearchVO tagSearchVO,
                                                                                  @Param("userDept") UserDeptDTO userDept);

    List<BusinessTaskAccountingDeptCountDTO> searchListBusinessDeptCountForManager(@Param("vo") BusinessTaskVO vo,
                                                                                   @Param("tagSearchVO") TagSearchVO tagSearchVO,
                                                                                   @Param("userDept") UserDeptDTO userDept);

    List<BusinessTaskAccountingDeptCountDTO> searchListBusinessDeptCountForMy(@Param("vo") BusinessTaskVO vo,
                                                                              @Param("tagSearchVO") TagSearchVO tagSearchVO);

    List<BusinessTask> getByPeriodAndBankAccountNumber(@Param("vo") RemoteBusinessTaskSearchV2VO vo);

    List<BusinessTask> getBatchNeedCheckBusinessTaskByAccountingCashier(@Param("cashierList") List<CustomerServiceCashierAccounting> cashierList);

    Long selectLessTaskAdminDeptByDeptIds(@Param("deptIds") List<Long> deptIds);
}
