package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationDTO;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationResult;
import com.bxm.customer.domain.dto.valueAdded.BaseImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.TemplateParseResult;
import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import org.springframework.web.multipart.MultipartFile;
import java.io.InputStream;

import java.util.List;
import java.util.Map;

/**
 * 导入操作策略接口
 *
 * 定义不同操作类型的导入处理策略
 * 每种操作类型对应不同的业务逻辑：
 * 1. DELIVERY - 交付操作：修改状态为已交付待确认
 * 2. SUPPLEMENT_DELIVERY - 补充附件：不修改状态，仅添加文件
 * 3. DEDUCTION - 扣款操作：修改状态为已扣款
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
public interface ImportOperationStrategy {

    /**
     * 获取策略支持的操作类型
     *
     * @return 支持的操作类型
     */
    ValueAddedBatchImportOperationType getSupportedOperationType();



    /**
     * 验证交付单状态是否允许当前操作
     *
     * @param order 交付单
     * @throws IllegalArgumentException 当状态不允许操作时抛出
     */
    void validateOrderStatus(ValueAddedDeliveryOrder order);

    /**
     * 获取目标状态（如果需要修改状态）
     *
     * @param currentStatus 当前状态
     * @return 目标状态，如果不需要修改状态则返回null
     */
    String getTargetStatus(String currentStatus);



    /**
     * 处理文件保存
     *
     * @param order 交付单
     * @param extractedFiles 解压后的文件信息
     * @param importDTO 导入DTO
     * @return 保存成功的文件数量
     */
    int processFileSaving(
            ValueAddedDeliveryOrder order,
            Map<String, String> extractedFiles,
            BatchImportOperationDTO importDTO
    );

    /**
     * 解析Excel文件为DTO列表（纯文件解析，不包含业务校验）
     *
     * 根据操作类型解析Excel文件内容为对应的DTO列表
     * 仅负责文件解析，不进行任何业务校验
     *
     * @param templateStream Excel模板文件流
     * @return 解析后的DTO列表
     * @throws Exception 当解析失败时抛出
     */
    List<? extends BaseImportExcelDTO> parseExcelFile(InputStream templateStream) throws Exception;

    /**
     * 校验解析后的数据（纯数据校验，不包含文件解析）
     *
     * 对解析后的DTO列表进行业务规则校验
     * 包括基础校验和特定业务校验
     *
     * @param dataList 解析后的DTO列表
     * @return 校验结果，包含成功数据和错误信息
     */
    TemplateParseResult validateData(List<? extends BaseImportExcelDTO> dataList);

    /**
     * 解析Excel模板文件并进行业务校验（组合方法）
     *
     * 根据操作类型解析Excel文件内容为对应的DTO列表，并进行业务规则校验
     * 这是一个便利方法，内部组合调用 parseExcelFile 和 validateData
     *
     * @param templateStream Excel模板文件流
     * @return 包含解析结果和校验错误的结果对象
     * @throws Exception 当解析失败时抛出
     */
    default TemplateParseResult parseTemplateFile(InputStream templateStream) throws Exception {
        // 先解析文件
        List<? extends BaseImportExcelDTO> dataList = parseExcelFile(templateStream);
        // 再校验数据
        return validateData(dataList);
    }



    /**
     * 获取操作描述
     *
     * @return 操作描述
     */
    default String getOperationDescription() {
        return getSupportedOperationType().getDescription();
    }
}
