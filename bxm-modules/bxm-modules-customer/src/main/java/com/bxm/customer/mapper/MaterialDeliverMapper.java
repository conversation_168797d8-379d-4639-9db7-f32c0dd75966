package com.bxm.customer.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.MaterialDeliver;
import com.bxm.customer.domain.dto.CommonDeptCountDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialDeliverInventoryDetailDTO;
import com.bxm.customer.domain.dto.materialDeliver.MaterialFileInventoryV2DTO;
import com.bxm.customer.domain.dto.workBench.MaterialDeliverStatisticDTO;
import com.bxm.customer.domain.vo.materialDeliver.MaterialDeliverInventoryDetailSearchVO;
import com.bxm.customer.domain.vo.materialDeliver.MaterialDeliverSearchVO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 材料交接单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
@Mapper
public interface MaterialDeliverMapper extends BaseMapper<MaterialDeliver>
{
    /**
     * 查询材料交接单
     * 
     * @param id 材料交接单主键
     * @return 材料交接单
     */
    public MaterialDeliver selectMaterialDeliverById(Long id);

    /**
     * 查询材料交接单列表
     * 
     * @param materialDeliver 材料交接单
     * @return 材料交接单集合
     */
    public List<MaterialDeliver> selectMaterialDeliverList(MaterialDeliver materialDeliver);

    /**
     * 新增材料交接单
     * 
     * @param materialDeliver 材料交接单
     * @return 结果
     */
    public int insertMaterialDeliver(MaterialDeliver materialDeliver);

    /**
     * 修改材料交接单
     * 
     * @param materialDeliver 材料交接单
     * @return 结果
     */
    public int updateMaterialDeliver(MaterialDeliver materialDeliver);

    /**
     * 删除材料交接单
     * 
     * @param id 材料交接单主键
     * @return 结果
     */
    public int deleteMaterialDeliverById(Long id);

    /**
     * 批量删除材料交接单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverByIds(Long[] ids);

    List<MaterialDeliverDTO> materialDeliverList(IPage<MaterialDeliverDTO> result, @Param("vo") MaterialDeliverSearchVO vo,
                                                 @Param("userDept") UserDeptDTO userDept);

    MaterialDeliverStatisticDTO materialDeliverStatistic(@Param("userDept") UserDeptDTO userDeptDTO);

    List<CommonDeptCountDTO> materialCommitDeptCountList(@Param("userDept") UserDeptDTO userDeptDTO,
                                                         @Param("vo") MaterialDeliverSearchVO vo);

    List<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailList(IPage<MaterialDeliverInventoryDetailDTO> result,
                                                                               @Param("vo") MaterialDeliverInventoryDetailSearchVO vo,
                                                                               @Param("type") Integer type,
                                                                               @Param("pushStatus") Integer pushStatus);

    List<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailList(@Param("vo") MaterialDeliverInventoryDetailSearchVO vo,
                                                                               @Param("type") Integer type,
                                                                               @Param("pushStatus") Integer pushStatus);

    List<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailListV2(IPage<MaterialFileInventoryV2DTO> result,
                                                                          @Param("vo") MaterialDeliverInventoryDetailSearchVO vo,
                                                                          @Param("type") Integer type);

    List<MaterialDeliverInventoryDetailDTO> materialDeliverInventoryDetailListByIds(@Param("ids") List<Long> ids);

    List<MaterialFileInventoryV2DTO> materialDeliverInventoryDetailListByIdsV2(@Param("ids") List<Long> ids);
}
