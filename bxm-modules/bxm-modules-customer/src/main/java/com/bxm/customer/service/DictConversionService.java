package com.bxm.customer.service;

import com.bxm.common.core.web.domain.TextVO;
import com.bxm.common.security.utils.DictUtils;
import com.bxm.system.api.domain.SysDictData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 字典转换服务
 *
 * 提供字典数据查询和转换的统一服务，遵循单一职责原则
 *
 * <AUTHOR>
 * @date 2025-09-03
 */
@Slf4j
@Service
public class DictConversionService {

    /**
     * 根据字典类型获取字典标签列表（中文描述）
     *
     * @param dictType 字典类型
     * @return 字典标签列表
     */
    public List<String> getDictLabels(String dictType) {
        try {
            List<SysDictData> dictDataList = DictUtils.getDictCache(dictType);

            if (dictDataList == null || dictDataList.isEmpty()) {
                log.warn("未找到字典类型 {} 的数据", dictType);
                return new ArrayList<>();
            }

            return dictDataList.stream()
                    .map(SysDictData::getDictLabel)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取字典标签失败，字典类型: {}", dictType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据字典类型获取TextVO列表（key为英文，value为中文）
     *
     * @param dictType 字典类型
     * @return TextVO列表
     */
    public List<TextVO> getDictTextVOList(String dictType) {
        try {
            List<SysDictData> dictDataList = DictUtils.getDictCache(dictType);

            if (dictDataList == null || dictDataList.isEmpty()) {
                log.warn("未找到字典类型 {} 的数据", dictType);
                return new ArrayList<>();
            }

            return dictDataList.stream()
                    .map(dictData -> TextVO.of(dictData.getDictValue(), dictData.getDictLabel()))
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取字典TextVO列表失败，字典类型: {}", dictType, e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据字典类型和值获取对应的标签
     *
     * @param dictType 字典类型
     * @param dictValue 字典值
     * @return 字典标签，如果未找到返回原值
     */
    public String getDictLabel(String dictType, String dictValue) {
        try {
            List<SysDictData> dictDataList = DictUtils.getDictCache(dictType);

            if (dictDataList == null || dictDataList.isEmpty()) {
                return dictValue;
            }

            return dictDataList.stream()
                    .filter(dictData -> dictValue.equals(dictData.getDictValue()))
                    .map(SysDictData::getDictLabel)
                    .findFirst()
                    .orElse(dictValue);
        } catch (Exception e) {
            log.error("获取字典标签失败，字典类型: {}, 字典值: {}", dictType, dictValue, e);
            return dictValue;
        }
    }
}
