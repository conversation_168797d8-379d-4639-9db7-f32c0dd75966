package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerServiceOperate;

/**
 * 材料、资料交接Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-11
 */
@Mapper
public interface CustomerServiceOperateMapper extends BaseMapper<CustomerServiceOperate>
{
    /**
     * 查询材料、资料交接
     * 
     * @param id 材料、资料交接主键
     * @return 材料、资料交接
     */
    public CustomerServiceOperate selectCustomerServiceOperateById(Long id);

    /**
     * 查询材料、资料交接列表
     * 
     * @param customerServiceOperate 材料、资料交接
     * @return 材料、资料交接集合
     */
    public List<CustomerServiceOperate> selectCustomerServiceOperateList(CustomerServiceOperate customerServiceOperate);

    /**
     * 新增材料、资料交接
     * 
     * @param customerServiceOperate 材料、资料交接
     * @return 结果
     */
    public int insertCustomerServiceOperate(CustomerServiceOperate customerServiceOperate);

    /**
     * 修改材料、资料交接
     * 
     * @param customerServiceOperate 材料、资料交接
     * @return 结果
     */
    public int updateCustomerServiceOperate(CustomerServiceOperate customerServiceOperate);

    /**
     * 删除材料、资料交接
     * 
     * @param id 材料、资料交接主键
     * @return 结果
     */
    public int deleteCustomerServiceOperateById(Long id);

    /**
     * 批量删除材料、资料交接
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerServiceOperateByIds(Long[] ids);
}
