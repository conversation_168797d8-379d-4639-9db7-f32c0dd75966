package com.bxm.customer.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.dto.newCustomerTransfer.NewCustomerTransferDTO;
import com.bxm.system.api.domain.dept.UserDeptDTO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 新户流转客户信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-01
 */
@Mapper
public interface NewCustomerInfoMapper extends BaseMapper<NewCustomerInfo>
{
    /**
     * 查询新户流转客户信息
     * 
     * @param id 新户流转客户信息主键
     * @return 新户流转客户信息
     */
    public NewCustomerInfo selectNewCustomerInfoById(Long id);

    /**
     * 查询新户流转客户信息列表
     * 
     * @param newCustomerInfo 新户流转客户信息
     * @return 新户流转客户信息集合
     */
    public List<NewCustomerInfo> selectNewCustomerInfoList(NewCustomerInfo newCustomerInfo);

    /**
     * 新增新户流转客户信息
     * 
     * @param newCustomerInfo 新户流转客户信息
     * @return 结果
     */
    public int insertNewCustomerInfo(NewCustomerInfo newCustomerInfo);

    /**
     * 修改新户流转客户信息
     * 
     * @param newCustomerInfo 新户流转客户信息
     * @return 结果
     */
    public int updateNewCustomerInfo(NewCustomerInfo newCustomerInfo);

    /**
     * 删除新户流转客户信息
     * 
     * @param id 新户流转客户信息主键
     * @return 结果
     */
    public int deleteNewCustomerInfoById(Long id);

    /**
     * 批量删除新户流转客户信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerInfoByIds(Long[] ids);

    List<NewCustomerTransferDTO> selectNewCustomerTransferList(IPage<NewCustomerTransferDTO> iPage,
                                                               @Param("keyWord") String keyWord,
                                                               @Param("status") Integer status, @Param("taxType") Integer taxType,
                                                               @Param("queryDeptId") Long queryDeptId,
                                                               @Param("customerIds") List<Long> customerIds, @Param("tagName") String tagName,
                                                               @Param("tagIncludeFlag") Integer tagIncludeFlag, @Param("userDeptDTO") UserDeptDTO userDeptDTO,
                                                               @Param("isHeadquarters") Boolean isHeadquarters, @Param("businessDeptIdList") List<Long> businessDeptIdList,
                                                               @Param("firstPeriodStart") Integer firstPeriodStart, @Param("firstPeriodEnd") Integer firstPeriodEnd);
}
