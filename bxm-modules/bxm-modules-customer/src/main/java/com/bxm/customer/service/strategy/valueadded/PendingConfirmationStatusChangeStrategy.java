package com.bxm.customer.service.strategy.valueadded;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.strategy.ValueAddedAbstractStatusChangeStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * 待确认到已确认待扣款状态变更策略
 *
 * 处理从"已交付待确认"状态到"已确认待扣款"状态的转换变更
 *
 * 支持的状态转换：
 * PENDING_CONFIRMATION -> CONFIRMED_PENDING_DEDUCTION (会计确认正常)
 *
 * <AUTHOR>
 * @date 2025-08-30
 */
@Slf4j
@Component
public class PendingConfirmationStatusChangeStrategy extends ValueAddedAbstractStatusChangeStrategy {

    @Override
    public ValueAddedDeliveryOrderStatus getSupport() {
        return ValueAddedDeliveryOrderStatus.PENDING_CONFIRMATION;
    }

    @Override
    public List<ValueAddedDeliveryOrderStatus> getAllowedTargetStatuses() {
        // 待确认状态可以转换到：待扣款、已完成、待交付(逆向)
        return Arrays.asList(
            ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION,  // 正向：确认后进入扣款流程
            ValueAddedDeliveryOrderStatus.COMPLETED,                    // 正向：直接完成
            ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY    // 逆向：退回到待交付
        );
    }

    @Override
    protected void validateSpecificTransition(ValueAddedDeliveryOrder order,
                                            StatusChangeRequestDTO request,
                                            ValueAddedDeliveryOrderStatus targetStatus) {
        // 只处理 CONFIRMED_PENDING_DEDUCTION 目标状态
        if (targetStatus == ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION) {
            validateContactInfo(order);
            if (order.getAccountingInfo() == null) {
                throw new IllegalArgumentException("账务类型信息不能为空");
            }
            if (order.getAccountingPeriodStart() == null || order.getAccountingPeriodEnd() == null) {
                throw new IllegalArgumentException("账期开始和结束时间不能为空");
            }
            if (order.getAccountingPeriodStart() > order.getAccountingPeriodEnd()) {
                throw new IllegalArgumentException("账期开始时间不能晚于结束时间");
            }
        } else if (targetStatus == ValueAddedDeliveryOrderStatus.COMPLETED) {

        } else if (targetStatus == ValueAddedDeliveryOrderStatus.SUBMITTED_PENDING_DELIVERY) {

        } else {
            throwUnsupportedTransition("已交付待确认", request.getTargetStatus());
        }
    }


}
