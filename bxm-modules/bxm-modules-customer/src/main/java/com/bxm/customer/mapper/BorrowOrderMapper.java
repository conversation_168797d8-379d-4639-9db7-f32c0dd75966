package com.bxm.customer.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.customer.domain.dto.borrow.BorrowOrderDTO;
import com.bxm.customer.domain.vo.borrow.BorrowOrderSearchVO;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.BorrowOrder;
import org.apache.ibatis.annotations.Param;

/**
 * 借阅单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-20
 */
@Mapper
public interface BorrowOrderMapper extends BaseMapper<BorrowOrder>
{
    /**
     * 查询借阅单
     * 
     * @param id 借阅单主键
     * @return 借阅单
     */
    public BorrowOrder selectBorrowOrderById(Long id);

    /**
     * 查询借阅单列表
     * 
     * @param borrowOrder 借阅单
     * @return 借阅单集合
     */
    public List<BorrowOrder> selectBorrowOrderList(BorrowOrder borrowOrder);

    /**
     * 新增借阅单
     * 
     * @param borrowOrder 借阅单
     * @return 结果
     */
    public int insertBorrowOrder(BorrowOrder borrowOrder);

    /**
     * 修改借阅单
     * 
     * @param borrowOrder 借阅单
     * @return 结果
     */
    public int updateBorrowOrder(BorrowOrder borrowOrder);

    /**
     * 删除借阅单
     * 
     * @param id 借阅单主键
     * @return 结果
     */
    public int deleteBorrowOrderById(Long id);

    /**
     * 批量删除借阅单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBorrowOrderByIds(Long[] ids);

    List<BorrowOrderDTO> borrowOrderList(IPage<BorrowOrderDTO> result, @Param("vo") BorrowOrderSearchVO vo, @Param("deptIds") List<Long> deptIds,
                                         @Param("isHeadquarters") Boolean isHeadquarters);
}
