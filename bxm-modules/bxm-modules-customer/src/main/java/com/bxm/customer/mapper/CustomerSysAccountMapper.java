package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.CustomerSysAccount;

/**
 * 客户服务系统账号Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-05-09
 */
@Mapper
public interface CustomerSysAccountMapper extends BaseMapper<CustomerSysAccount>
{
    /**
     * 查询客户服务系统账号
     * 
     * @param id 客户服务系统账号主键
     * @return 客户服务系统账号
     */
    public CustomerSysAccount selectCustomerSysAccountById(Long id);

    /**
     * 查询客户服务系统账号列表
     * 
     * @param customerSysAccount 客户服务系统账号
     * @return 客户服务系统账号集合
     */
    public List<CustomerSysAccount> selectCustomerSysAccountList(CustomerSysAccount customerSysAccount);

    /**
     * 新增客户服务系统账号
     * 
     * @param customerSysAccount 客户服务系统账号
     * @return 结果
     */
    public int insertCustomerSysAccount(CustomerSysAccount customerSysAccount);

    /**
     * 修改客户服务系统账号
     * 
     * @param customerSysAccount 客户服务系统账号
     * @return 结果
     */
    public int updateCustomerSysAccount(CustomerSysAccount customerSysAccount);

    /**
     * 删除客户服务系统账号
     * 
     * @param id 客户服务系统账号主键
     * @return 结果
     */
    public int deleteCustomerSysAccountById(Long id);

    /**
     * 批量删除客户服务系统账号
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCustomerSysAccountByIds(Long[] ids);
}
