package com.bxm.customer.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.bxm.customer.domain.ValueAddedItemType;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedItemTypeVO;

import java.util.List;

/**
 * 增值事项类型Service接口
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
public interface IValueAddedItemTypeService extends IService<ValueAddedItemType> {

    /**
     * 查询增值事项类型列表（VO格式）
     *
     * 查询所有未删除的增值事项类型，并转换为VO格式返回
     *
     * @return 增值事项类型VO列表
     */
    List<ValueAddedItemTypeVO> listItemTypeVO();

    /**
     * 根据事项类型编码查询事项ID列表
     *
     * @param itemTypeCode 事项类型编码（ACCOUNTING 或 TAX）
     * @return 事项ID列表
     */
    List<Integer> getItemIdsByItemTypeCode(String itemTypeCode);

}
