package com.bxm.customer.service.strategy.valueadded.importoperation;

import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.valueAdded.BatchImportOperationDTO;
import com.bxm.customer.domain.dto.valueAdded.BaseImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.DeductionImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.TemplateParseResult;
import com.bxm.customer.domain.enums.ValueAddedBatchImportOperationType;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 扣款导入策略实现
 *
 * 处理扣款操作的导入逻辑：
 * 1. 验证交付单状态必须为"已确认待扣款"
 * 2. 修改状态为"已扣款"
 * 3. 保存相关附件文件到value_added_file表
 *
 * <AUTHOR>
 * @date 2025-08-25
 */
@Slf4j
@Component
public class DeductionImportStrategy extends AbstractImportOperationStrategy {

    @Autowired
    private IValueAddedDeliveryOrderService deliveryOrderService;

    @Autowired
    private IValueAddedFileService fileService;

    @Override
    public ValueAddedBatchImportOperationType getSupportedOperationType() {
        return ValueAddedBatchImportOperationType.DEDUCTION;
    }



    @Override
    public void validateOrderStatus(ValueAddedDeliveryOrder order) {
        String currentStatus = order.getStatus();
        if (!ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode().equals(currentStatus)) {
            throw new IllegalArgumentException(
                    String.format("Delivery order %s status does not allow deduction operation, current status: %s, required status: %s",
                            order.getDeliveryOrderNo(),
                            ValueAddedDeliveryOrderStatus.getByCode(currentStatus).getDescription(),
                            ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getDescription())
            );
        }
    }

    @Override
    public String getTargetStatus(String currentStatus) {
        // 扣款操作：已确认待扣款 -> 已扣款
        if (ValueAddedDeliveryOrderStatus.CONFIRMED_PENDING_DEDUCTION.getCode().equals(currentStatus)) {
            return ValueAddedDeliveryOrderStatus.DEDUCTION_COMPLETED.getCode();
        }
        return null;
    }



    @Override
    public int processFileSaving(
            ValueAddedDeliveryOrder order,
            Map<String, String> extractedFiles,
            BatchImportOperationDTO importDTO) {

        int savedCount = 0;
        String deliveryOrderNo = order.getDeliveryOrderNo();

        for (Map.Entry<String, String> entry : extractedFiles.entrySet()) {
            try {
                String fileName = entry.getKey();
                String filePath = entry.getValue();

                // Check if file name is related to delivery order number
                if (isFileRelatedToOrder(fileName, deliveryOrderNo)) {
                    ValueAddedFile file = new ValueAddedFile();
                    file.setDeliveryOrderNo(deliveryOrderNo);
                    file.setFileName(fileName);
                    file.setFileUrl(filePath);
                    file.setFileType(1); // 1-交付材料附件（扣款凭证）
                    file.setStatus(1); // 1-处理完成
                    file.setIsDel(false);
                    file.setRemark("批量扣款导入凭证");
                    file.setCreateBy(SecurityUtils.getUserId().toString());

                    boolean saved = fileService.save(file);
                    if (saved) {
                        savedCount++;
                        log.debug("Deduction voucher file saved successfully: {} -> {}", fileName, deliveryOrderNo);
                    }
                }
            } catch (Exception e) {
                log.warn("Deduction voucher file save failed: {}, error: {}", entry.getKey(), e.getMessage());
            }
        }

        return savedCount;
    }

    /**
     * 判断文件是否与交付单相关
     * 扣款操作的文件通常是扣款凭证或相关证明文件
     */
    private boolean isFileRelatedToOrder(String fileName, String deliveryOrderNo) {
        if (fileName == null || deliveryOrderNo == null) {
            return false;
        }

        String upperFileName = fileName.toUpperCase();
        String upperOrderNo = deliveryOrderNo.toUpperCase();

        // Deduction file matching rules:
        // 1. File name contains delivery order number
        // 2. Or file name contains keywords like "deduction", "voucher"
        return upperFileName.contains(upperOrderNo) ||
               upperFileName.contains("DEDUCTION") ||
               upperFileName.contains("VOUCHER") ||
               upperFileName.contains("PAYMENT") ||
               upperFileName.contains("RECEIPT");
    }

    @Override
    protected Class<? extends BaseImportExcelDTO> getExcelDtoClass() {
        return DeductionImportExcelDTO.class;
    }

    @Override
    protected TemplateParseResult validateSpecificData(List<? extends BaseImportExcelDTO> validData) {
        // 扣款操作暂时不需要特殊校验，直接返回成功结果
        return TemplateParseResult.success(validData);
    }
}
