package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.SettlementOrderFile;

/**
 * 结算单附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-15
 */
@Mapper
public interface SettlementOrderFileMapper extends BaseMapper<SettlementOrderFile>
{
    /**
     * 查询结算单附件
     * 
     * @param id 结算单附件主键
     * @return 结算单附件
     */
    public SettlementOrderFile selectSettlementOrderFileById(Long id);

    /**
     * 查询结算单附件列表
     * 
     * @param settlementOrderFile 结算单附件
     * @return 结算单附件集合
     */
    public List<SettlementOrderFile> selectSettlementOrderFileList(SettlementOrderFile settlementOrderFile);

    /**
     * 新增结算单附件
     * 
     * @param settlementOrderFile 结算单附件
     * @return 结果
     */
    public int insertSettlementOrderFile(SettlementOrderFile settlementOrderFile);

    /**
     * 修改结算单附件
     * 
     * @param settlementOrderFile 结算单附件
     * @return 结果
     */
    public int updateSettlementOrderFile(SettlementOrderFile settlementOrderFile);

    /**
     * 删除结算单附件
     * 
     * @param id 结算单附件主键
     * @return 结果
     */
    public int deleteSettlementOrderFileById(Long id);

    /**
     * 批量删除结算单附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSettlementOrderFileByIds(Long[] ids);
}
