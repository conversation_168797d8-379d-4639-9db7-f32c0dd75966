package com.bxm.customer.service.strategy;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.dto.StatusChangeRequestDTO;
import com.bxm.customer.domain.enums.ValueAddedDeliveryOrderStatus;

import java.util.List;

/**
 * 状态变更策略接口
 *
 * 定义增值交付单状态转换的变更规则接口
 * 每个具体状态的变更逻辑通过实现此接口来完成
 *
 * <AUTHOR>
 * @date 2025-08-13
 */
public interface StatusChangeStrategy {

    /**
     * 获取当前策略支持的当前状态
     *
     * @return 支持的当前状态
     */
    ValueAddedDeliveryOrderStatus getSupport();

    /**
     * 判断当前策略是否支持指定的当前状态
     *
     * @param currentStatus 当前状态
     * @return 是否支持此当前状态
     */
    boolean supports(ValueAddedDeliveryOrderStatus currentStatus);

    /**
     * 验证从当前状态到目标状态的转换是否合法
     *
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @return 是否支持此状态转换
     */
    boolean isValidTransition(ValueAddedDeliveryOrderStatus currentStatus, ValueAddedDeliveryOrderStatus targetStatus);

    /**
     * 执行状态转换前的验证逻辑
     *
     * 验证内容包括但不限于：
     * 1. 业务规则验证（如是否满足转换条件）
     * 2. 权限验证（如操作人是否有权限执行此转换）
     * 3. 数据完整性验证（如必要字段是否已填写）
     * 4. 外部依赖验证（如相关业务数据是否就绪）
     *
     * @param order 增值交付单实体
     * @param request 状态变更请求
     * @throws IllegalArgumentException 当验证失败时抛出，包含具体的失败原因
     * @throws RuntimeException 当系统异常时抛出
     */
    void validate(ValueAddedDeliveryOrder order, StatusChangeRequestDTO request);

    /**
     * 获取当前状态允许转换到的目标状态列表
     *
     * @return 允许转换到的目标状态列表
     */
    List<ValueAddedDeliveryOrderStatus> getAllowedTargetStatuses();

    /**
     * 获取策略名称，用于日志记录和调试
     *
     * @return 策略名称
     */
    default String getStrategyName() {
        return this.getClass().getSimpleName();
    }
}
