package com.bxm.customer.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.BaseVO;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ExportService {

    @Autowired
    private AsyncService asyncService;

    /**
     * 通用异步导出方法
     *
     * @param title            导出文件标题
     * @param vo               查询条件对象
     * @param deptId           部门 ID
     * @param serviceMethod    分页查询方法
     * @param downloadType     下载记录类型
     * @param dtoClass         导出数据的 DTO 类型
     * @param downloadRecordService 下载记录服务
     * @param <T>              查询条件类型
     * @param <R>              导出数据类型
     * @param hideFields       需要隐藏的列
     */
    public <T, R> void exportAsync (
            String title,
            T vo,
            Long deptId,
            BiFunction<Long, T, IPage<R>> serviceMethod,
            DownloadType downloadType,
            Class<R> dtoClass,
            IDownloadRecordService downloadRecordService,
            String... hideFields) {

        // 创建下载记录
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, downloadType);

        CompletableFuture.runAsync(() -> {
            try {
                List<R> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 1000;

                while (true) {
                    // 设置分页参数
                    if (vo instanceof BaseVO) {
                        ((BaseVO) vo).setPageNum(pageNum);
                        ((BaseVO) vo).setPageSize(pageSize);
                    }

                    // 调用分页查询方法
                    IPage<R> page = serviceMethod.apply(deptId, vo);
                    if (!ObjectUtils.isEmpty(page.getRecords())) {
                        list.addAll(page.getRecords());
                        pageNum++;
                    } else {
                        break;
                    }
                }

                // 更新数据条数
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());

                // 导出 Excel 并上传 OSS
                ExcelUtil<R> util = new ExcelUtil<>(dtoClass);
                if (!ObjectUtils.isEmpty(hideFields)) {
                    util.hideColumn(hideFields);
                }
                asyncService.uploadExport(util, list, title, downloadRecordId);

            } catch (Exception e) {
                // 更新错误信息
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
    }

    /**
     * 通用异步导出方法
     *
     * @param title            导出文件标题
     * @param vo               查询条件对象
     * @param deptId           部门 ID
     * @param serviceMethod    分页查询方法
     * @param downloadType     下载记录类型
     * @param dtoClass         导出数据的 DTO 类型
     * @param downloadRecordService 下载记录服务
     * @param <T>              查询条件类型
     * @param <R>              查询数据类型
     * @param <S>              导出数据类型
     */
    public <T, R, S> void exportCopyAsync (
            String title,
            T vo,
            Long deptId,
            BiFunction<Long, T, IPage<R>> serviceMethod,
            DownloadType downloadType,
            Class<S> dtoClass,
            IDownloadRecordService downloadRecordService) {

        // 创建下载记录
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, downloadType);

        CompletableFuture.runAsync(() -> {
            try {
                List<R> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 1000;

                while (true) {
                    // 设置分页参数
                    if (vo instanceof BaseVO) {
                        ((BaseVO) vo).setPageNum(pageNum);
                        ((BaseVO) vo).setPageSize(pageSize);
                    }

                    // 调用分页查询方法
                    IPage<R> page = serviceMethod.apply(deptId, vo);
                    if (!ObjectUtils.isEmpty(page.getRecords())) {
                        list.addAll(page.getRecords());
                        pageNum++;
                    } else {
                        break;
                    }
                }

                // 更新数据条数
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());

                List<S> exportList = list.stream().map(row -> {
                    S s = null;
                    try {
                        s = dtoClass.getDeclaredConstructor().newInstance();
                        BeanUtils.copyProperties(row, s);
                        return s;
                    } catch (Exception e) {
                        log.error("数据转换失败: {}", e.getMessage());
                        throw new RuntimeException("数据转换失败", e);
                    }
                }).collect(Collectors.toList());
                // 导出 Excel 并上传 OSS
                ExcelUtil<S> util = new ExcelUtil<>(dtoClass);
                asyncService.uploadExport(util, exportList, title, downloadRecordId);

            } catch (Exception e) {
                // 更新错误信息
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
    }

    /**
     * 通用异步导出方法
     *
     * @param param            查询条件参数
     * @param paramClass       查询条件参数类型
     * @param downloadRecordId  下载记录 ID
     * @param title            导出文件标题
     * @param deptId           部门 ID
     * @param serviceMethod    分页查询方法
     * @param dtoClass         导出数据的 DTO 类型
     * @param downloadRecordService 下载记录服务
     * @param <T>              查询条件类型
     * @param <R>              导出数据类型
     */
    public <T, R> void exportAsyncRetry (
            String param,
            Class<T> paramClass,
            Long downloadRecordId,
            String title,
            Long deptId,
            BiFunction<Long, T, IPage<R>> serviceMethod,
            Class<R> dtoClass,
            IDownloadRecordService downloadRecordService,
            String... hideFields) {

        CompletableFuture.runAsync(() -> {
            try {
                T vo = JSONObject.parseObject(param, paramClass);
                List<R> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 1000;

                while (true) {
                    // 设置分页参数
                    if (vo instanceof BaseVO) {
                        ((BaseVO) vo).setPageNum(pageNum);
                        ((BaseVO) vo).setPageSize(pageSize);
                    }

                    // 调用分页查询方法
                    IPage<R> page = serviceMethod.apply(deptId, vo);
                    if (!ObjectUtils.isEmpty(page.getRecords())) {
                        list.addAll(page.getRecords());
                        pageNum++;
                    } else {
                        break;
                    }
                }

                // 更新数据条数
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());

                // 导出 Excel 并上传 OSS
                ExcelUtil<R> util = new ExcelUtil<>(dtoClass);
                if (!StringUtils.isEmpty(hideFields)) {
                    util.hideColumn(hideFields);
                }
                asyncService.uploadExport(util, list, title, downloadRecordId);

            } catch (Exception e) {
                // 更新错误信息
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
    }

    public <T, S, R> void exportAsyncRetryWithFiles (
            String param,
            Class<T> paramClass,
            Long downloadRecordId,
            String title,
            Long deptId,
            String exportTypes,
            BiFunction<Long, T, IPage<R>> serviceMethod,
            Class<S> dtoClass,
            BiFunction<List<R>, String, List<CommonFileVO>> fileBuilderFunction,
            IDownloadRecordService downloadRecordService,
            String... hideFields) {

        CompletableFuture.runAsync(() -> {
            try {
                T vo = JSONObject.parseObject(param, paramClass);
                List<R> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 1000;

                while (true) {
                    // 设置分页参数
                    if (vo instanceof BaseVO) {
                        ((BaseVO) vo).setPageNum(pageNum);
                        ((BaseVO) vo).setPageSize(pageSize);
                    }

                    // 调用分页查询方法
                    IPage<R> page = serviceMethod.apply(deptId, vo);
                    if (!ObjectUtils.isEmpty(page.getRecords())) {
                        list.addAll(page.getRecords());
                        pageNum++;
                    } else {
                        break;
                    }
                }

                // 更新数据条数
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());

                // 根据导出类型决定导出逻辑
                if (StringUtils.isEmpty(exportTypes)) {
                    // 单 sheet 导出
                    ExcelUtil<S> util = new ExcelUtil<>(dtoClass);
                    if (!ObjectUtils.isEmpty(hideFields)) {
                        util.hideColumn(hideFields);
                    }
                    List<S> exportList = list.stream().map(row -> {
                        S s = null;
                        try {
                            s = dtoClass.getDeclaredConstructor().newInstance();
                            BeanUtils.copyProperties(row, s);
                            return s;
                        } catch (Exception e) {
                            log.error("数据转换失败: {}", e.getMessage());
                            throw new RuntimeException("数据转换失败", e);
                        }
                    }).collect(Collectors.toList());
                    asyncService.uploadExport(util, exportList, title, downloadRecordId);
                } else {
                    // 多 sheet 导出
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(title, dtoClass);
                    List<S> exportList = list.stream().map(row -> {
                        S s = null;
                        try {
                            s = dtoClass.getDeclaredConstructor().newInstance();
                            BeanUtils.copyProperties(row, s);
                            return s;
                        } catch (Exception e) {
                            log.error("数据转换失败: {}", e.getMessage());
                            throw new RuntimeException("数据转换失败", e);
                        }
                    }).collect(Collectors.toList());
                    dataMap.put(title, exportList);

                    // 构建文件内容
                    List<CommonFileVO> files = fileBuilderFunction.apply(list, exportTypes);

                    // 上传文件
                    asyncService.uploadExport(files, dataMap, sheetClassMap, title, downloadRecordId);
                }

            } catch (Exception e) {
                // 更新错误信息
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
    }

    public <T, S, R> void exportAsyncWithFiles(
            String title,
            Long deptId,
            T vo,
            BiFunction<Long, T, IPage<R>> pageListFunction,
            DownloadType downloadType,
            String exportTypes,
            Class<S> dtoClass,
            IDownloadRecordService downloadRecordService,
            BiFunction<List<R>, String, List<CommonFileVO>> fileBuilderFunction,
            String... hideFields) {

        // 创建下载记录
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, downloadType);

        CompletableFuture.runAsync(() -> {
            try {
                List<R> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 1000;

                while (true) {
                    // 设置分页参数
                    if (vo instanceof BaseVO) {
                        ((BaseVO) vo).setPageNum(pageNum);
                        ((BaseVO) vo).setPageSize(pageSize);
                    }

                    // 调用分页查询方法
                    IPage<R> page = pageListFunction.apply(deptId, vo);
                    if (!ObjectUtils.isEmpty(page.getRecords())) {
                        list.addAll(page.getRecords());
                        pageNum++;
                    } else {
                        break;
                    }
                }

                // 更新数据条数
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());

                // 根据导出类型决定导出逻辑
                if (StringUtils.isEmpty(exportTypes)) {
                    // 单 sheet 导出
                    ExcelUtil<S> util = new ExcelUtil<>(dtoClass);
                    if (!ObjectUtils.isEmpty(hideFields)) {
                        util.hideColumn(hideFields);
                    }
                    List<S> exportList = list.stream().map(row -> {
                        S s = null;
                        try {
                            s = dtoClass.getDeclaredConstructor().newInstance();
                            BeanUtils.copyProperties(row, s);
                            return s;
                        } catch (Exception e) {
                            log.error("数据转换失败: {}", e.getMessage());
                            throw new RuntimeException("数据转换失败", e);
                        }
                    }).collect(Collectors.toList());
                    asyncService.uploadExport(util, exportList, title, downloadRecordId);
                } else {
                    // 多 sheet 导出
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(title, dtoClass);
                    List<S> exportList = list.stream().map(row -> {
                        S s = null;
                        try {
                            s = dtoClass.getDeclaredConstructor().newInstance();
                            BeanUtils.copyProperties(row, s);
                            return s;
                        } catch (Exception e) {
                            log.error("数据转换失败: {}", e.getMessage());
                            throw new RuntimeException("数据转换失败", e);
                        }
                    }).collect(Collectors.toList());
                    dataMap.put(title, exportList);

                    // 构建文件内容
                    List<CommonFileVO> files = fileBuilderFunction.apply(list, exportTypes);

                    // 上传文件
                    asyncService.uploadExport(files, dataMap, sheetClassMap, title, downloadRecordId);
                }

            } catch (Exception e) {
                // 更新错误信息
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
    }
}
