package com.bxm.customer.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.redis.service.RedisService;
import com.bxm.customer.config.BatchOperationConfig;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.FileStatusInfo;
import com.bxm.customer.domain.dto.valueAdded.*;
import com.bxm.customer.utils.DynamicExcelExportUtil;
import com.bxm.customer.domain.enums.ValueAddedBizType;
import com.bxm.customer.domain.enums.ValueAddedEntryType;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.domain.enums.ValueAddedProcessStatus;
import com.bxm.customer.domain.enums.ValidationErrorType;
import com.bxm.customer.domain.query.valueAdded.EmployeeQuery;
import com.bxm.customer.domain.vo.valueAdded.SocialInsuranceVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.mapper.ValueAddedEmployeeMapper;
import com.bxm.customer.service.IValueAddedEmployeeService;
import com.bxm.customer.service.IValueAddedFileService;

import com.bxm.customer.utils.ValidateUtil;
import com.bxm.file.api.RemoteFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 增值员工信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Slf4j
@Service
public class ValueAddedEmployeeServiceImpl extends ServiceImpl<ValueAddedEmployeeMapper, ValueAddedEmployee>
        implements IValueAddedEmployeeService {



    @Autowired
    private IValueAddedFileService valueAddedFileService;

    @Autowired
    private RemoteFileService remoteFileService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private BatchOperationConfig batchOperationConfig;



    @Override
    public ValueAddedEmployee getByDeliveryOrderAndIdNumber(String deliveryOrderNo, String idNumber, Integer bizType) {
        if (StringUtils.isEmpty(deliveryOrderNo) || StringUtils.isEmpty(idNumber) || bizType == null) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                .eq(ValueAddedEmployee::getIdNumber, idNumber.trim())
                .eq(ValueAddedEmployee::getBizType, bizType);

        return getOne(queryWrapper);
    }

    @Override
    public ValueAddedEmployee getOneEmployeeByDeliveryOrderAndBizType(String deliveryOrderNo, Integer bizType) {
        if (StringUtils.isEmpty(deliveryOrderNo) || bizType == null) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                .eq(ValueAddedEmployee::getBizType, bizType)
                .orderByDesc(ValueAddedEmployee::getId)
                .last("LIMIT 1");

        return getOne(queryWrapper);
    }

    @Override
    public ValueAddedEmployee getByDeliveryOrderAndMobile(String deliveryOrderNo, String mobile, Integer bizType) {
        if (StringUtils.isEmpty(deliveryOrderNo) || StringUtils.isEmpty(mobile) || bizType == null) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                .eq(ValueAddedEmployee::getMobile, mobile.trim())
                .eq(ValueAddedEmployee::getBizType, bizType);

        return getOne(queryWrapper);
    }

    @Override
    public ValueAddedEmployee getByDeliveryOrderAndIdNumberOrMobile(String deliveryOrderNo, String idNumber, String mobile, Integer bizType) {
        if (StringUtils.isEmpty(deliveryOrderNo) || bizType == null) {
            return null;
        }

        // 身份证号和手机号至少要有一个
        if (StringUtils.isEmpty(idNumber) && StringUtils.isEmpty(mobile)) {
            return null;
        }

        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                .eq(ValueAddedEmployee::getBizType, bizType);

        // 构建OR条件：身份证号匹配 OR 手机号匹配
        queryWrapper.and(wrapper -> {
            boolean hasCondition = false;
            if (StringUtils.isNotEmpty(idNumber)) {
                wrapper.eq(ValueAddedEmployee::getIdNumber, idNumber.trim().toUpperCase());
                hasCondition = true;
            }
            if (StringUtils.isNotEmpty(mobile)) {
                if (hasCondition) {
                    wrapper.or();
                }
                wrapper.eq(ValueAddedEmployee::getMobile, mobile.trim());
            }
        });

        return getOne(queryWrapper,false);
    }



    @Override
    public List<SocialInsuranceDTO> getSocialInsuranceDetailForExport(String deliveryOrderNo) {
        log.info("Getting social insurance detail for export, deliveryOrderNo: {}", deliveryOrderNo);

        // 查询社保业务类型的员工数据
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                   .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.SOCIAL_INSURANCE.getCode()); // 社医保

        List<ValueAddedEmployee> employees = this.list(queryWrapper);
        List<SocialInsuranceDTO> exportList = new ArrayList<>();

        for (ValueAddedEmployee employee : employees) {
            SocialInsuranceDTO exportDTO = SocialInsuranceDTO.builder()
                    .operationType(convertOperationType(employee.getOperationType()))
                    .employeeName(employee.getEmployeeName())
                    .idNumber(employee.getIdNumber())
                    .mobile(StringUtils.isNotEmpty(employee.getMobile()) ? employee.getMobile() : "-")
                    .remark(StringUtils.isNotEmpty(employee.getRemark()) ? employee.getRemark() : "-")
                    .socialInsuranceBase(formatSocialInsuranceBase(employee.getGrossSalary()))
                    .build();

            // 解析社保信息
            parseSocialInsurance(employee.getSocialInsurance(), exportDTO);

            exportList.add(exportDTO);
        }

        log.info("Found {} social insurance records for export", exportList.size());
        return exportList;
    }

    /**
     * 获取个税明细数据用于Excel导出
     *
     * @param deliveryOrderNo 交付单编号
     * @return 个税明细导出数据列表
     */
    @Override
    public List<PersonalTaxDetailExportDTO> getPersonalTaxDetailForExport(String deliveryOrderNo) {
        log.info("Getting personal tax detail for export, deliveryOrderNo: {}", deliveryOrderNo);

        // 查询个税明细业务类型的员工数据
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                   .eq(ValueAddedEmployee::getBizType, ValueAddedBizType.PERSONAL_TAX.getCode()); // 个税明细

        List<ValueAddedEmployee> employees = this.list(queryWrapper);
        List<PersonalTaxDetailExportDTO> exportList = new ArrayList<>();

        for (ValueAddedEmployee employee : employees) {
            PersonalTaxDetailExportDTO exportDTO = PersonalTaxDetailExportDTO.builder()
                    .operationType(convertOperationType(employee.getOperationType()))
                    .employeeName(employee.getEmployeeName())
                    .idNumber(employee.getIdNumber())
                    .mobile(StringUtils.isNotEmpty(employee.getMobile()) ? employee.getMobile() : "-")
                    .remark(StringUtils.isNotEmpty(employee.getRemark()) ? employee.getRemark() : "-")
                    .socialInsuranceBase(formatSocialInsuranceBase(employee.getGrossSalary()))
                    .grossSalary(employee.getGrossSalary() != null ? employee.getGrossSalary().toString() : "-")
                    .housingFundPersonalAmount(employee.getProvidentFundPersonal() != null ? employee.getProvidentFundPersonal().toString() : "-")
                    .build();

            // 解析社保信息（个税明细现在也使用 socialInsurance 字段）
            parseSocialInsurance(employee.getSocialInsurance(), exportDTO);

            // 解析扩展信息中的其他字段
            parsePersonalTaxExtendInfo(employee.getExtendInfo(), exportDTO);

            exportList.add(exportDTO);
        }

        log.info("Found {} personal tax records for export", exportList.size());
        return exportList;
    }

    @Override
    public void processBatchEmployeeData(Long fileId, Integer bizType, Boolean overrideExisting) {
        log.info("Starting process batch employee data, fileId: {}, bizType: {}", fileId, bizType);

        // 异步处理数据
        CompletableFuture.runAsync(() -> {
            processBatchDataFromFile(fileId, bizType, overrideExisting);
        });
    }

    @Override
    public String getProcessStatusByFileId(Long fileId) {
        return valueAddedFileService.getFileProcessStatus(fileId);
    }

    /**
     * 从文件异步处理批量数据
     * deliveryOrderNo从文件记录中获取
     */
    private void processBatchDataFromFile(Long fileId, Integer bizType, Boolean overrideExisting) {
        log.info("Processing batch data from file, fileId: {}, bizType: {}", fileId, bizType);

        try {
            // 1. 获取文件信息
            ValueAddedFile fileRecord = valueAddedFileService.getFileById(fileId);
            if (fileRecord == null) {
                throw new RuntimeException("文件记录不存在");
            }

            // 获取交付单编号
            String deliveryOrderNo = fileRecord.getDeliveryOrderNo();

            // 2. 更新状态为处理中
            updateFileStatus(fileId, ValueAddedProcessStatus.PROCESSING.getCode(), "开始解析Excel文件", 0, 0, 0);

            // 3. 下载并解析Excel文件
            String fullFileUrl = remoteFileService.getFullFileUrl(fileRecord.getFileUrl()).getDataThrowException();
            if (StringUtils.isEmpty(fullFileUrl)) {
                throw new RuntimeException("获取文件下载地址失败");
            }

            // 4. 解析Excel文件
            List<? extends BaseDetailExportDTO> importList = parseExcelFromUrl(fullFileUrl, bizType);
            if (importList == null || importList.isEmpty()) {
                throw new RuntimeException("Excel文件中没有有效数据");
            }

            updateFileStatus(fileId, ValueAddedProcessStatus.PROCESSING.getCode(), "开始校验数据", importList.size(), 0, 0);

            // 5. 校验数据
            EmployeeValidationResult validationResult = validateEmployeeList(importList, bizType, deliveryOrderNo, overrideExisting);

            // 6. 将校验结果和覆盖标志存入Redis
            cacheValidationResult(fileId, validationResult);
            cacheOverrideFlag(fileId, overrideExisting);

            // 7. 更新文件状态
            updateFileStatus(fileId, ValueAddedProcessStatus.COMPLETED.getCode(),
                           "校验完成", validationResult.getTotalCount(),
                           validationResult.getSuccessCount(), validationResult.getFailCount());

            log.info("Batch validation completed, fileId: {}, total: {}, success: {}, fail: {}",
                    fileId, validationResult.getTotalCount(), validationResult.getSuccessCount(), validationResult.getFailCount());

        } catch (Exception e) {
            log.error("Batch processing failed, fileId: {}", fileId, e);
            updateFileStatus(fileId, ValueAddedProcessStatus.FAILED.getCode(), "处理失败: " + e.getMessage(), 0, 0, 0);
        }
    }

    /**
     * 缓存校验结果到Redis
     */
    private void cacheValidationResult(Long fileId, EmployeeValidationResult validationResult) {
        try {
            // 缓存成功数据
            if (validationResult.getSuccessList() != null && !validationResult.getSuccessList().isEmpty()) {
                String successKey = CacheConstants.EMPLOYEE_BATCH_VALIDATION_SUCCESS + fileId;
                redisService.setLargeCacheList(
                    successKey,
                    validationResult.getSuccessList(),
                    batchOperationConfig.getRedisBatchSize(),
                    batchOperationConfig.getErrorDataCacheTimeSeconds(),
                    TimeUnit.SECONDS
                );
                log.info("Cached {} success records for fileId: {}", validationResult.getSuccessList().size(), fileId);
            }

            // 缓存失败数据（使用动态错误包装器）
            if (validationResult.hasErrorWrappers()) {
                String failKey = CacheConstants.EMPLOYEE_BATCH_VALIDATION_FAIL + fileId;
                redisService.setLargeCacheList(
                    failKey,
                    validationResult.getErrorWrapperList(),
                    batchOperationConfig.getRedisBatchSize(),
                    batchOperationConfig.getErrorDataCacheTimeSeconds(),
                    TimeUnit.SECONDS
                );
                log.info("Cached {} error wrapper records for fileId: {}", validationResult.getErrorWrapperList().size(), fileId);
            }

        } catch (Exception e) {
            log.error("Failed to cache validation result for fileId: {}", fileId, e);
            throw new RuntimeException("缓存校验结果失败: " + e.getMessage());
        }
    }

    /**
     * 缓存覆盖标志到Redis
     */
    private void cacheOverrideFlag(Long fileId, Boolean overrideExisting) {
        try {
            String overrideKey = CacheConstants.EMPLOYEE_BATCH_OVERRIDE_FLAG + fileId;
            redisService.setCacheObject(overrideKey, overrideExisting,
                                      batchOperationConfig.getErrorDataCacheTimeSeconds().longValue(), TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("Failed to cache override flag for fileId: {}", fileId, e);
            throw new RuntimeException("缓存覆盖标志失败: " + e.getMessage());
        }
    }

    /**
     * 覆盖模式保存员工数据
     *
     * @param employeeList 员工数据列表
     * @param deliveryOrderNo 交付单号
     * @param bizType 业务类型
     * @return 保存结果
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean saveWithOverride(List<ValueAddedEmployee> employeeList, String deliveryOrderNo, Integer bizType) {
        try {
            // 1. 先删除现有数据
            deleteExistingEmployeeData(deliveryOrderNo, bizType);
            log.info("Deleted existing employee data for deliveryOrderNo: {}, bizType: {}", deliveryOrderNo, bizType);
            // 2. 批量保存新数据
            boolean saveResult = batchSaveEmployees(employeeList);
            if (!saveResult) {
                throw new RuntimeException("批量保存员工数据失败");
            }
            return true;
        } catch (Exception e) {
            log.error("Failed to save employees with override mode for deliveryOrderNo: {}, bizType: {}",
                     deliveryOrderNo, bizType, e);
            throw new RuntimeException("覆盖模式保存失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ValueAddedEmployee> saveNonDuplicateList(List<ValueAddedEmployee> employeeList) {
        if (employeeList == null || employeeList.isEmpty()) {
            log.warn("Employee list is empty, returning empty list");
            return Collections.emptyList();
        }

        try {
            // 获取交付单编号和业务类型（所有记录应该是同一个交付单和业务类型）
            String deliveryOrderNo = employeeList.get(0).getDeliveryOrderNo();
            Integer bizType = employeeList.get(0).getBizType();

            log.info("Starting saveNonDuplicateList for deliveryOrderNo: {}, bizType: {}, records: {}",
                    deliveryOrderNo, bizType, employeeList.size());

            // 查询数据库中已存在的数据的deliveryOrderNo+身份证号组合
            Set<String> existingKeys = getExistingEmployeeKeys(deliveryOrderNo, bizType);
            log.info("Found {} existing employee keys in database", existingKeys.size());

            // 过滤出未重复的记录
            List<ValueAddedEmployee> deduplicatedList = employeeList.stream()
                    .filter(employee -> {
                        String key = buildEmployeeKey(employee.getDeliveryOrderNo(), employee.getIdNumber());
                        boolean isNotDuplicate = !existingKeys.contains(key);
                        if (!isNotDuplicate) {
                            log.debug("Filtered duplicate employee: {} ({})",
                                    employee.getEmployeeName(), employee.getIdNumber());
                        }
                        return isNotDuplicate;
                    })
                    .collect(Collectors.toList());

            // 保存去重后的数据
            if (!deduplicatedList.isEmpty()) {
                boolean saveResult = batchSaveEmployees(deduplicatedList);
                if (!saveResult) {
                    throw new RuntimeException("批量保存员工数据失败");
                }
            }

            log.info("SaveNonDuplicateList completed: original {} records, after deduplication {} records, saved {} records",
                    employeeList.size(), deduplicatedList.size(), deduplicatedList.size());

            return deduplicatedList;

        } catch (Exception e) {
            log.error("Error during saveNonDuplicateList process", e);
            throw new RuntimeException("保存非重复数据失败: " + e.getMessage());
        }
    }

    /**
     * 删除现有员工数据
     *
     * @param deliveryOrderNo 交付单号
     * @param bizType 业务类型
     */
    private void deleteExistingEmployeeData(String deliveryOrderNo, Integer bizType) {
        try {
            LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                       .eq(ValueAddedEmployee::getBizType, bizType);
            int deletedCount = baseMapper.delete(queryWrapper);
        } catch (Exception e) {
            log.error("Failed to delete existing employee data for deliveryOrderNo: {}, bizType: {}",
                     deliveryOrderNo, bizType, e);
            throw new RuntimeException("删除现有员工数据失败: " + e.getMessage());
        }
    }

    /**
     * 批量保存员工数据
     *
     * @param employeeList 员工数据列表
     * @return 保存结果
     */
    private boolean batchSaveEmployees(List<ValueAddedEmployee> employeeList) {
        try {
            if (employeeList == null || employeeList.isEmpty()) {
                log.warn("Employee list is empty, skip batch save");
                return true;
            }

            boolean result = saveBatch(employeeList);
            log.info("Batch saved {} employees, result: {}", employeeList.size(), result);
            return result;
        } catch (Exception e) {
            log.error("Failed to batch save {} employees", employeeList.size(), e);
            throw new RuntimeException("批量保存员工数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取数据库中已存在的员工键值（deliveryOrderNo+身份证号）
     *
     * @param deliveryOrderNo 交付单编号
     * @param bizType 业务类型
     * @return 已存在的键值集合
     */
    private Set<String> getExistingEmployeeKeys(String deliveryOrderNo, Integer bizType) {
        try {
            // 查询数据库中已存在的该交付单和业务类型下的所有员工
            LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                       .eq(ValueAddedEmployee::getBizType, bizType)
                       .select(ValueAddedEmployee::getDeliveryOrderNo, ValueAddedEmployee::getIdNumber);

            List<ValueAddedEmployee> existingEmployees = list(queryWrapper);
            return existingEmployees.stream()
                    .filter(emp -> StringUtils.isNotEmpty(emp.getIdNumber()))
                    .map(emp -> buildEmployeeKey(emp.getDeliveryOrderNo(), emp.getIdNumber()))
                    .collect(Collectors.toSet());

        } catch (Exception e) {
            log.error("Error querying existing employees for deliveryOrderNo: {}, bizType: {}",
                    deliveryOrderNo, bizType, e);
            return Collections.emptySet();
        }
    }

    /**
     * 构建员工唯一键值（deliveryOrderNo+身份证号）
     *
     * @param deliveryOrderNo 交付单编号
     * @param idNumber 身份证号
     * @return 唯一键值
     */
    private String buildEmployeeKey(String deliveryOrderNo, String idNumber) {
        return deliveryOrderNo + "_" + (idNumber != null ? idNumber.trim() : "");
    }

    /**
     * 更新文件处理状态
     */
    private void updateFileStatus(Long fileId, Integer status, String message,
                                 Integer totalCount, Integer successCount, Integer failCount) {
        try {
            // 使用DTO封装状态信息，便于使用BeanUtils
            FileStatusInfo statusInfo = FileStatusInfo.builder()
                    .status(status)
                    .message(message)
                    .totalCount(totalCount)
                    .successCount(successCount)
                    .failCount(failCount)
                    .hasErrorFile(failCount != null && failCount > 0)
                    .updateTime(System.currentTimeMillis())
                    .build();

            // 直接将DTO对象序列化为JSON - 使用FastJSON2替代ObjectMapper
            String statusJson = JSON.toJSONString(statusInfo);

            valueAddedFileService.updateFileProcessStatus(fileId, status, statusJson);
        } catch (Exception e) {
            log.error("Update file status failed, fileId: {}", fileId, e);
        }
    }

    /**
     * 从URL解析Excel文件（根据业务类型选择不同的DTO）
     */
    private List<? extends BaseDetailExportDTO> parseExcelFromUrl(String fileUrl, Integer bizType) throws Exception {
        try (InputStream inputStream = new URL(fileUrl).openStream()) {
            if (bizType == ValueAddedBizType.SOCIAL_INSURANCE.getCode()) {
                // 社医保使用SocialInsuranceDTO
                ExcelUtil<SocialInsuranceDTO> util = new ExcelUtil<>(SocialInsuranceDTO.class);
                return util.importExcel(inputStream);
            } else if (bizType == ValueAddedBizType.PERSONAL_TAX.getCode()) {
                // 个税明细使用PersonalTaxDetailImportDTO
                ExcelUtil<PersonalTaxDetailImportDTO> util = new ExcelUtil<>(PersonalTaxDetailImportDTO.class);
                return util.importExcel(inputStream);
            } else {
                throw new IllegalArgumentException("不支持的业务类型: " + bizType);
            }
        }
    }

    /**
     * 从URL解析Excel文件（兼容旧版本）
     */
    private List<SocialInsuranceDTO> parseExcelFromUrl(String fileUrl) throws Exception {
        try (InputStream inputStream = new URL(fileUrl).openStream()) {
            ExcelUtil<SocialInsuranceDTO> util = new ExcelUtil<>(SocialInsuranceDTO.class);
            return util.importExcel(inputStream);
        }
    }

    /**
     * 将导入DTO转换为员工VO（支持多种业务类型）
     */
    private ValueAddedEmployeeVO convertToEmployeeVO(Object importDTO, Integer bizType, String deliveryOrderNo) {
        if (bizType == ValueAddedBizType.SOCIAL_INSURANCE.getCode()) {
            return convertSocialInsuranceToEmployeeVO((SocialInsuranceDTO) importDTO, bizType, deliveryOrderNo);
        } else if (bizType == ValueAddedBizType.PERSONAL_TAX.getCode()) {
            return convertPersonalTaxToEmployeeVO((PersonalTaxDetailImportDTO) importDTO, bizType, deliveryOrderNo);
        } else {
            throw new IllegalArgumentException("不支持的业务类型: " + bizType);
        }
    }

    /**
     * 将社保导入DTO转换为员工VO
     * 使用BeanUtils优化字段复制，提高代码可维护性
     */
    private ValueAddedEmployeeVO convertSocialInsuranceToEmployeeVO(SocialInsuranceDTO importDTO, Integer bizType, String deliveryOrderNo) {
        // 验证必填字段
        validateImportDTO(importDTO);

        // 创建目标VO对象
        ValueAddedEmployeeVO employeeVO = new ValueAddedEmployeeVO();

        // 使用BeanUtils复制同名字段（如employeeName, idNumber, mobile, remark等）
        BeanUtils.copyProperties(importDTO, employeeVO);
        employeeVO.setBizType(bizType != null ? bizType : ValueAddedBizType.SOCIAL_INSURANCE.getCode());
        employeeVO.setEntryType(ValueAddedEntryType.BATCH_ADD.getCode());

        // 设置交付单编号
        employeeVO.setDeliveryOrderNo(deliveryOrderNo);

        // 处理需要转换的字段
        employeeVO.setOperationType(convertOperationTypeFromString(importDTO.getOperationType()));
        employeeVO.setGrossSalary(convertSocialInsuranceBase(importDTO.getSocialInsuranceBase()));
        employeeVO.setSocialInsurance(buildSocialInsuranceFromImportDTO(importDTO));

        // 清理字符串字段的空白字符
        trimStringFields(employeeVO);
        return employeeVO;
    }

    /**
     * 将个税明细导入DTO转换为员工VO
     */
    private ValueAddedEmployeeVO convertPersonalTaxToEmployeeVO(PersonalTaxDetailImportDTO importDTO, Integer bizType, String deliveryOrderNo) {
        // 验证必填字段
        validatePersonalTaxImportDTO(importDTO);

        // 创建目标VO对象
        ValueAddedEmployeeVO employeeVO = new ValueAddedEmployeeVO();

        // 使用BeanUtils复制同名字段（如employeeName, idNumber, mobile, remark等）
        BeanUtils.copyProperties(importDTO, employeeVO);
        employeeVO.setBizType(bizType);
        employeeVO.setEntryType(ValueAddedEntryType.BATCH_ADD.getCode());

        // 设置交付单编号
        employeeVO.setDeliveryOrderNo(deliveryOrderNo);

        // 处理需要转换的字段
        employeeVO.setOperationType(convertOperationTypeFromString(importDTO.getOperationType()));

        // 个税明细特有字段处理
        employeeVO.setGrossSalary(convertStringToBigDecimal(importDTO.getGrossSalary()));
        employeeVO.setProvidentFundPersonal(convertStringToBigDecimal(importDTO.getHousingFundPersonalAmount()));

        // 构建社保信息对象（统一使用 socialInsurance 字段）
        SocialInsuranceVO socialInsurance = buildSocialInsuranceFromImportDTO(importDTO);
        employeeVO.setSocialInsurance(socialInsurance);

        // 构建个税明细扩展信息JSON字符串（不再包含社保信息）
        employeeVO.setExtendInfo(buildPersonalTaxExtendInfo(importDTO));

        // 清理字符串字段的空白字符
        trimStringFields(employeeVO);

        return employeeVO;
    }

    /**
     * 验证导入DTO的必填字段
     */
    private void validateImportDTO(SocialInsuranceDTO importDTO) {
        if (StringUtils.isEmpty(importDTO.getEmployeeName())) {
            throw new IllegalArgumentException("员工姓名不能为空");
        }
        if (StringUtils.isEmpty(importDTO.getIdNumber())) {
            throw new IllegalArgumentException("身份证号不能为空");
        }
        if (StringUtils.isEmpty(importDTO.getOperationType())) {
            throw new IllegalArgumentException("操作方式不能为空");
        }
    }

    /**
     * 验证个税明细导入DTO的必填字段
     */
    private void validatePersonalTaxImportDTO(PersonalTaxDetailImportDTO importDTO) {
        if (StringUtils.isEmpty(importDTO.getEmployeeName())) {
            throw new IllegalArgumentException("员工姓名不能为空");
        }
        if (StringUtils.isEmpty(importDTO.getIdNumber())) {
            throw new IllegalArgumentException("身份证号不能为空");
        }
        if (StringUtils.isEmpty(importDTO.getOperationType())) {
            throw new IllegalArgumentException("操作方式不能为空");
        }
    }

    /**
     * 转换字符串为BigDecimal（通用方法）
     */
    private BigDecimal convertStringToBigDecimal(String value) {
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("数值格式不正确: " + value);
        }
    }

    /**
     * 转换社保基数字符串为BigDecimal
     */
    private BigDecimal convertSocialInsuranceBase(String socialInsuranceBase) {
        if (StringUtils.isEmpty(socialInsuranceBase)) {
            return null;
        }
        try {
            return new BigDecimal(socialInsuranceBase);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("社保基数格式不正确: " + socialInsuranceBase);
        }
    }

    /**
     * 清理字符串字段的空白字符
     */
    private void trimStringFields(ValueAddedEmployeeVO employeeVO) {
        if (StringUtils.isNotEmpty(employeeVO.getEmployeeName())) {
            employeeVO.setEmployeeName(employeeVO.getEmployeeName().trim());
        }
        if (StringUtils.isNotEmpty(employeeVO.getIdNumber())) {
            employeeVO.setIdNumber(employeeVO.getIdNumber().trim());
        }
        if (StringUtils.isNotEmpty(employeeVO.getMobile())) {
            employeeVO.setMobile(employeeVO.getMobile().trim());
        }
        if (StringUtils.isNotEmpty(employeeVO.getRemark())) {
            employeeVO.setRemark(employeeVO.getRemark().trim());
        }
    }

    /**
     * 转换操作方式字符串为数字
     */
    private Integer convertOperationTypeFromString(String operationType) {
        if (StringUtils.isEmpty(operationType)) {
            throw new IllegalArgumentException("操作方式不能为空");
        }

        String trimmed = operationType.trim();
        switch (trimmed) {
            case "增员":
                return ValueAddedOperationType.REMIND.getCode();
            case "更正":
                return ValueAddedOperationType.CORRECTION.getCode();
            case "减员":
                return ValueAddedOperationType.REDUCTION.getCode();
            default:
                throw new IllegalArgumentException("不支持的操作方式: " + operationType + "，支持的方式：增员、更正、减员");
        }
    }

    /**
     * 从导入DTO构建社保信息对象（通用方法）
     *
     * @param importDTO 导入DTO对象，可以是 SocialInsuranceDTO 或 PersonalTaxDetailImportDTO
     * @return 社保信息对象
     */
    private SocialInsuranceVO buildSocialInsuranceFromImportDTO(BaseDetailExportDTO importDTO) {
        try {
            return SocialInsuranceVO.builder()
                    .yangLao(SocialInsuranceVO.convertFromString(importDTO.getYangLao()))
                    .shiYe(SocialInsuranceVO.convertFromString(importDTO.getShiYe()))
                    .gongShang(SocialInsuranceVO.convertFromString(importDTO.getGongShang()))
                    .yiLiao(SocialInsuranceVO.convertFromString(importDTO.getYiLiao()))
                    .shengYu(SocialInsuranceVO.convertFromString(importDTO.getShengYu()))
                    .qiTa(SocialInsuranceVO.convertFromString(importDTO.getQiTa())) // 从DTO读取其他保险状态
                    .build();
        } catch (Exception e) {
            log.error("Build social insurance from import DTO failed", e);
            throw new RuntimeException("构建社保信息失败: " + e.getMessage());
        }
    }

    /**
     * 构建个税明细扩展信息JSON字符串（不再包含社保信息）
     */
    private String buildPersonalTaxExtendInfo(PersonalTaxDetailImportDTO importDTO) {
        Map<String, Object> extendMap = new HashMap<>();

        // 添加个税明细特有字段
        extendMap.put("housing_fund_personal_amount", importDTO.getHousingFundPersonalAmount());
//        extendMap.put("other", importDTO.getOther());

        // 不再添加保险信息，社保信息统一存储在 socialInsurance 字段中

        try {
            // 使用FastJSON2替代ObjectMapper
            return JSON.toJSONString(extendMap);
        } catch (Exception e) {
            log.error("Build personal tax extend info failed", e);
            throw new RuntimeException("构建个税明细扩展信息失败: " + e.getMessage());
        }
    }



    /**
     * 转换操作方式为中文显示
     */
    private String convertOperationType(Integer operationType) {
        if (operationType == null) {
            return "-";
        }
        switch (operationType) {
            case 1:
                return "增员";
            case 2:
                return "更正";
            case 3:
                return "减员";
            default:
                return "-";
        }
    }

    /**
     * 格式化社保基数
     */
    private String formatSocialInsuranceBase(BigDecimal grossSalary) {
        if (grossSalary == null) {
            return "-";
        }
        return grossSalary.toString();
    }

    /**
     * 解析社保信息对象
     */
    private void parseSocialInsurance(SocialInsuranceVO socialInsurance,
                                           BaseDetailExportDTO exportDTO) {
        // 设置默认值
        exportDTO.setYangLao("-");
        exportDTO.setShiYe("-");
        exportDTO.setGongShang("-");
        exportDTO.setYiLiao("-");
        exportDTO.setShengYu("-");
        exportDTO.setQiTa("-");

        if (socialInsurance == null) {
            return;
        }

        try {
            // 直接从对象获取属性值
            exportDTO.setYangLao(SocialInsuranceVO.convertToString(socialInsurance.getYangLao()));
            exportDTO.setShiYe(SocialInsuranceVO.convertToString(socialInsurance.getShiYe()));
            exportDTO.setGongShang(SocialInsuranceVO.convertToString(socialInsurance.getGongShang()));
            exportDTO.setYiLiao(SocialInsuranceVO.convertToString(socialInsurance.getYiLiao()));
            exportDTO.setShengYu(SocialInsuranceVO.convertToString(socialInsurance.getShengYu()));
            exportDTO.setQiTa(SocialInsuranceVO.convertToString(socialInsurance.getQiTa()));
        } catch (Exception e) {
            log.warn("Parse social insurance failed: {}", socialInsurance, e);
        }
    }

    /**
     * 解析个税明细扩展信息
     */
    private void parsePersonalTaxExtendInfo(String extendInfo, PersonalTaxDetailExportDTO exportDTO) {
        if (StringUtils.isEmpty(extendInfo)) {
            exportDTO.setOther("-");
            return;
        }

        try {
            // 使用FastJSON2解析扩展信息
            @SuppressWarnings("unchecked")
            Map<String, Object> extendMap = JSON.parseObject(extendInfo, Map.class);

            // 解析其他字段
            String other = (String) extendMap.get("other");
            exportDTO.setOther(StringUtils.isNotEmpty(other) ? other : "-");

        } catch (Exception e) {
            log.warn("Parse personal tax extend info failed: {}", extendInfo, e);
            exportDTO.setOther("-");
        }
    }


    @Override
    public IPage<ValueAddedEmployeeVO> queryVOPage(IPage<ValueAddedEmployeeVO> page, EmployeeQuery query) {
        try {
            // 构建动态查询条件
            LambdaQueryWrapper<ValueAddedEmployee> wrapper = new LambdaQueryWrapper<>();

            // 业务类型是必填条件
            if (query.getBizType() != null) {
                wrapper.eq(ValueAddedEmployee::getBizType, query.getBizType());
            }

            // 可选查询条件
            if (StringUtils.isNotEmpty(query.getDeliveryOrderNo())) {
                wrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, query.getDeliveryOrderNo());
            }

            if (StringUtils.isNotEmpty(query.getEmployeeName())) {
                wrapper.like(ValueAddedEmployee::getEmployeeName, query.getEmployeeName());
            }

            if (StringUtils.isNotEmpty(query.getIdNumber())) {
                wrapper.eq(ValueAddedEmployee::getIdNumber, query.getIdNumber());
            }

            if (query.getOperationType() != null) {
                wrapper.eq(ValueAddedEmployee::getOperationType, query.getOperationType());
            }

            // 排序：默认创建时间倒序
            wrapper.orderByDesc(ValueAddedEmployee::getCreateTime);

            // 创建分页对象进行查询
            IPage<ValueAddedEmployee> entityPage = new Page<>(page.getCurrent(), page.getSize());
            entityPage = this.page(entityPage, wrapper);

            if (entityPage.getRecords().isEmpty()) {
                return page.setRecords(new ArrayList<>());
            }

            // 转换为VO对象并根据bizType进行适当转换
            List<ValueAddedEmployeeVO> voList = new ArrayList<>();
            for (ValueAddedEmployee employee : entityPage.getRecords()) {
                ValueAddedEmployeeVO vo = convertToVO(employee, query.getBizType());
                voList.add(vo);
            }

            return page.setRecords(voList).setTotal(entityPage.getTotal());
        } catch (Exception e) {
            log.error("Failed to query value added employees with pagination", e);
            throw new RuntimeException("分页查询增值员工信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将实体对象转换为VO对象，并根据bizType进行适当转换
     */
    private ValueAddedEmployeeVO convertToVO(ValueAddedEmployee employee, Integer bizType) {
        ValueAddedEmployeeVO vo = new ValueAddedEmployeeVO();
        BeanUtils.copyProperties(employee, vo);

        // 映射name字段
        mapNameFields(vo);

        // 根据不同的bizType进行特定的字段处理
        if (bizType != null) {
            switch (bizType) {
                case 1: // 社保明细
                    // 社保明细业务：重点展示社保套餐信息
                    optimizeForSocialInsurance(vo, employee);
                    break;
                case 2: // 个税明细
                    // 个税明细业务：重点展示个税计算相关信息
                    optimizeForPersonalTax(vo, employee);
                    break;
                default:
                    break;
            }
        }

        return vo;
    }

    /**
     * 映射name字段，将code转换为对应的中文名称
     */
    private void mapNameFields(ValueAddedEmployeeVO vo) {
        // 映射业务类型名称
        if (vo.getBizType() != null) {
            ValueAddedBizType bizTypeEnum = ValueAddedBizType.getByCode(vo.getBizType());
            vo.setBizTypeName(bizTypeEnum != null ? bizTypeEnum.getName() : null);
        }

        // 映射录入方式名称
        if (vo.getEntryType() != null) {
            ValueAddedEntryType entryTypeEnum = ValueAddedEntryType.getByCode(vo.getEntryType());
            vo.setEntryTypeName(entryTypeEnum != null ? entryTypeEnum.getName() : null);
        }

        if (vo.getOperationType() != null && vo.getBizType() != null) {
            vo.setOperationTypeName(getOperationTypeName(vo.getBizType(), vo.getOperationType()));
        }
    }

    /**
     * 根据业务类型和操作类型获取操作方式名称
     */
    private String getOperationTypeName(Integer bizType, Integer operationType) {
        ValueAddedBizType bizTypeEnum = ValueAddedBizType.getByCode(bizType);
        if (bizTypeEnum == null) {
            return null;
        }

        switch (bizTypeEnum) {
            case SOCIAL_INSURANCE:
            case PERSONAL_TAX:
                // 社医保和个税明细使用相同的操作类型
                switch (operationType) {
                    case 1:
                        return "增员";
                    case 2:
                        return "更正";
                    case 3:
                        return "减员";
                    default:
                        return null;
                }
            case NATIONAL_TAX_ACCOUNT:
                // 国税账号操作类型
                switch (operationType) {
                    case 1:
                        return "账号信息";
                    case 2:
                        return "会计实名";
                    default:
                        return null;
                }
            case PERSONAL_TAX_ACCOUNT:
                // 个税账号操作类型
                if (operationType == 1) {
                    return "个税账号添加";
                }
                return null;
            default:
                return null;
        }
    }

    /**
     * 针对社保明细业务优化VO对象
     *
     * @param vo VO对象
     * @param employee 实体对象
     */
    private void optimizeForSocialInsurance(ValueAddedEmployeeVO vo, ValueAddedEmployee employee) {
        // 社保明细业务重点关注社保套餐信息

        // 确保社保信息完整性：如果为空，提供默认值
        if (vo.getSocialInsurance() == null) {
            vo.setSocialInsurance(SocialInsuranceVO.createDefault());
            log.debug("Set default social insurance for employee: {}", employee.getEmployeeName());
        }
    }

    /**
     * 针对个税明细业务优化VO对象
     *
     * @param vo VO对象
     * @param employee 实体对象
     */
    private void optimizeForPersonalTax(ValueAddedEmployeeVO vo, ValueAddedEmployee employee) {
        // 个税明细业务重点关注工资和个税计算相关信息

        // 确保应发工资的有效性（个税明细要求更严格）
        if (vo.getGrossSalary() == null || vo.getGrossSalary().doubleValue() <= 0) {
            log.warn("Personal tax employee has invalid gross salary: employeeName={}, salary={}",
                    employee.getEmployeeName(), vo.getGrossSalary());
        }

        // 个税明细也需要社保信息用于个税计算，确保社保信息存在
        if (vo.getSocialInsurance() == null) {
            vo.setSocialInsurance(SocialInsuranceVO.createDefault());
            log.debug("Set default social insurance for personal tax employee: {}", employee.getEmployeeName());
        }

        // 个税明细业务可能有扩展信息（如"其他"字段），保持原有扩展信息
        // 这些信息对个税计算可能有用

        log.debug("Optimized VO for personal tax business: employeeName={}, grossSalary={}, socialInsurance={}",
                employee.getEmployeeName(), vo.getGrossSalary(), vo.getSocialInsurance());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> ids) {
        try {
            log.info("Starting batch delete operation for employee ids: {}", ids);
            // 执行批量删除
            boolean result = removeByIds(ids);
            if (result) {
                log.info("Batch delete operation completed successfully, deleted count: {}", ids.size());
            } else {
                log.warn("Batch delete operation failed for ids: {}", ids);
                throw new RuntimeException("批量删除操作失败");
            }
            return result;
        } catch (IllegalArgumentException e) {
            log.warn("Batch delete validation failed: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in batch delete operation for ids: {}", ids, e);
            throw new RuntimeException("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 校验员工数据列表
     *
     * @param importList 导入的员工数据列表
     * @param bizType 业务类型
     * @param deliveryOrderNo 交付单编号
     * @param overrideExisting 是否覆盖现有数据
     * @return 校验结果
     */
    private EmployeeValidationResult validateEmployeeList(List<? extends BaseDetailExportDTO> importList, Integer bizType,
                                                         String deliveryOrderNo, Boolean overrideExisting) {
        log.info("Starting validation for {} employees, bizType: {}, deliveryOrderNo: {}",
                importList.size(), bizType, deliveryOrderNo);

        ValidationContext ctx = buildValidationContext(importList, deliveryOrderNo, bizType, overrideExisting);

        List<ValueAddedEmployee> successList = new ArrayList<>();
        List<DynamicErrorExportWrapper<BaseDetailExportDTO>> errorWrapperList = new ArrayList<>();

        for (int i = 0; i < importList.size(); i++) {
            BaseDetailExportDTO dto = importList.get(i);
            int rowNumber = i + 2; // Excel行号从2开始

            try {
                // 收集该行所有字段的校验错误
                FieldValidationResult fieldResult = validateAllFields(dto, rowNumber, ctx, bizType);

                if (fieldResult.hasErrors()) {
                    // 创建动态错误包装器（显示完整字段+错误信息）
                    DynamicErrorExportWrapper<BaseDetailExportDTO> errorWrapper = createErrorExportWrapper(dto, fieldResult);
                    errorWrapperList.add(errorWrapper);
                } else {
                    // 转换为实体并收集
                    ValueAddedEmployee employee = convertToEmployee(dto, bizType, deliveryOrderNo);
                    successList.add(employee);
                }
            } catch (Exception e) {
                log.error("Validation error for row {}: {}", rowNumber, e.getMessage());
                // 对于系统异常，也创建动态错误包装器
                ValidationResult systemErrorResult = ValidationResult.withError("系统", e.getMessage(), "系统错误");
                DynamicErrorExportWrapper<BaseDetailExportDTO> errorWrapper =
                    DynamicErrorExportWrapper.fromValidationResult(dto, systemErrorResult);
                errorWrapperList.add(errorWrapper);
            }
        }

        return EmployeeValidationResult.builder()
                .successList(successList)
                .failList(null) // 不再使用传统错误列表
                .errorWrapperList(errorWrapperList)
                .totalCount(importList.size())
                .successCount(successList.size())
                .failCount(errorWrapperList.size())
                .build();
    }

    /**
     * 字段校验结果类，用于收集单行数据的所有字段错误
     */
    private static class FieldValidationResult {
        private final List<FieldError> errors = new ArrayList<>();

        void addError(String fieldName, String errorMessage, String errorType) {
            errors.add(new FieldError(fieldName, errorMessage, errorType));
        }

        boolean hasErrors() {
            return !errors.isEmpty();
        }


        /**
         * 创建动态错误详情导出包装器
         *
         * @param originalDto 原始DTO对象
         * @return 包含完整字段和错误信息的动态导出包装器
         */
        <T extends ExcelExportable> DynamicErrorExportWrapper<T> createErrorExportWrapper(T originalDto) {
            // 1. 转换为通用校验结果
            ValidationResult validationResult = toValidationResult();
            // 2. 创建动态包装器
            return DynamicErrorExportWrapper.fromValidationResult(originalDto, validationResult);
        }

        /**
         * 转换为通用校验结果
         *
         * @return 通用校验结果
         */
        ValidationResult toValidationResult() {
            ValidationResult result = new ValidationResult();
            for (FieldError error : errors) {
                result.addError(error.fieldName, error.errorMessage, error.errorType);
            }
            return result;
        }

        /**
         * 字段错误信息
         */
        public static class FieldError {
            public final String fieldName;
            public final String errorMessage;
            public final String errorType;

            public FieldError(String fieldName, String errorMessage, String errorType) {
                this.fieldName = fieldName;
                this.errorMessage = errorMessage;
                this.errorType = errorType;
            }
        }
    }

    /**
     * 执行所有字段的校验，收集所有错误信息
     */
    private FieldValidationResult validateAllFields(BaseDetailExportDTO dto, Integer rowNumber,
                                                   ValidationContext ctx, Integer bizType) {
        FieldValidationResult result = new FieldValidationResult();

        // 1. 必填字段校验（所有操作都需要）
        validateRequiredFieldsCollectErrors(dto, result);

        // 2. 格式校验（所有操作都需要）
        validateFieldFormatsCollectErrors(dto, result);

        // 3. 去重校验（表内 + 数据库）- 仅增员和更正需要
        validateDuplicatesCollectErrors(dto, ctx, result);

        // 4. 业务规则校验 - 仅增员和更正需要
        validateBusinessRulesCollectErrors(dto, bizType, result);

        return result;
    }



    /**
     * 创建动态错误详情导出包装器（新方法）
     *
     * @param dto 原始DTO对象
     * @param fieldResult 字段校验结果
     * @return 包含完整字段和错误信息的动态导出包装器
     */
    private <T extends ExcelExportable> DynamicErrorExportWrapper<T> createErrorExportWrapper(
            T dto, FieldValidationResult fieldResult) {
        return fieldResult.createErrorExportWrapper(dto);
    }

    /**
     * 导出动态错误详情到Excel
     *
     * @param validationResult 校验结果
     * @param fileName 文件名前缀
     * @return Excel文件字节数组
     */
    public byte[] exportErrorDetailsToExcel(EmployeeValidationResult validationResult, String fileName) {
        if (validationResult == null || !validationResult.hasErrorWrappers()) {
            log.warn("没有错误数据需要导出");
            return new byte[0];
        }

        try {
            // 1. 使用动态Excel导出工具导出
            return DynamicExcelExportUtil.exportToExcel(
                validationResult.getErrorWrapperList(),
                "错误详情"
            );
        } catch (Exception e) {
            log.error("导出错误详情Excel失败: {}", e.getMessage(), e);
            throw new RuntimeException("导出Excel失败", e);
        }
    }

    /** 校验上下文，封装复用信息与集合 */
    private static class ValidationContext {
        final Set<String> batchIdNumbers = new HashSet<>();
        final Set<String> batchMobiles = new HashSet<>();
        final Set<String> existingIdNumbers;
        final Set<String> existingMobiles;
        final boolean overrideExisting;

        ValidationContext(Set<String> existingIdNumbers, Set<String> existingMobiles, boolean overrideExisting) {
            this.existingIdNumbers = existingIdNumbers;
            this.existingMobiles = existingMobiles;
            this.overrideExisting = overrideExisting;
        }

        void record(BaseDetailExportDTO dto) {
            if (dto.getIdNumber() != null) batchIdNumbers.add(dto.getIdNumber());
            if (dto.getMobile() != null) batchMobiles.add(dto.getMobile());
        }
    }

    /** 构建校验上下文（便于方法复用与简化签名） */
    private ValidationContext buildValidationContext(List<? extends BaseDetailExportDTO> importList,
                                                     String deliveryOrderNo, Integer bizType,
                                                     boolean overrideExisting) {
        Set<String> existingIdNumbers = Collections.emptySet();
        Set<String> existingMobiles = Collections.emptySet();
        if (!overrideExisting) {
            existingIdNumbers = getExistingIdNumbers(deliveryOrderNo, bizType);
            existingMobiles = getExistingPhoneNumbers(deliveryOrderNo, bizType);
        }
        return new ValidationContext(existingIdNumbers, existingMobiles, overrideExisting);
    }


    /**
     * 获取数据库中已存在的身份证号
     */
    private Set<String> getExistingIdNumbers(String deliveryOrderNo, Integer bizType) {
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                .eq(ValueAddedEmployee::getBizType, bizType)
                .select(ValueAddedEmployee::getIdNumber);

        List<ValueAddedEmployee> existingEmployees = list(queryWrapper);
        return existingEmployees.stream()
                .map(ValueAddedEmployee::getIdNumber)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 获取数据库中已存在的手机号
     */
    private Set<String> getExistingPhoneNumbers(String deliveryOrderNo, Integer bizType) {
        LambdaQueryWrapper<ValueAddedEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ValueAddedEmployee::getDeliveryOrderNo, deliveryOrderNo)
                .eq(ValueAddedEmployee::getBizType, bizType)
                .select(ValueAddedEmployee::getMobile);

        List<ValueAddedEmployee> existingEmployees = list(queryWrapper);
        return existingEmployees.stream()
                .map(ValueAddedEmployee::getMobile)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    /**
     * 去重校验
     */
    private void validateDuplicates(BaseDetailExportDTO dto, Integer rowNumber,
                                  ValidationContext ctx, List<EmployeeValidationError> errors) {

        String idNumber = dto.getIdNumber();
        String phoneNumber = dto.getMobile();
        String employeeName = dto.getEmployeeName();

        // 身份证号去重校验：表内 + 数据库（在非覆盖模式）
        if (idNumber != null && !idNumber.trim().isEmpty()) {
            if (ctx.batchIdNumbers.contains(idNumber) || (!ctx.overrideExisting && ctx.existingIdNumbers.contains(idNumber))) {
                errors.add(EmployeeValidationError.createDuplicateError(
                    rowNumber, employeeName, idNumber, phoneNumber, "身份证号", "身份证号已存在"));
            } else {
                // 首次出现则记录在批次集合中
                ctx.batchIdNumbers.add(idNumber);
            }
        }

        // 手机号去重校验：表内 + 数据库（在非覆盖模式）
        if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
            if (ctx.batchMobiles.contains(phoneNumber) || (!ctx.overrideExisting && ctx.existingMobiles.contains(phoneNumber))) {
                errors.add(EmployeeValidationError.createDuplicateError(
                    rowNumber, employeeName, idNumber, phoneNumber, "手机号", "手机号已存在"));
            } else {
                // 首次出现则记录在批次集合中
                ctx.batchMobiles.add(phoneNumber);
            }
        }
    }

    /**
     * 必填字段校验（收集所有错误版本）
     */
    private void validateRequiredFieldsCollectErrors(BaseDetailExportDTO dto, FieldValidationResult result) {
        // 方式必填
        if (dto.getOperationType() == null || dto.getOperationType().trim().isEmpty()) {
            result.addError("方式", "方式必填", ValidationErrorType.REQUIRED.getCode());
        } else {
            // 方式值校验：只允许增员、减员、更正
            String operationType = dto.getOperationType().trim();
            if (!"增员".equals(operationType) && !"减员".equals(operationType) && !"更正".equals(operationType)) {
                result.addError("方式", "方式非法", ValidationErrorType.FORMAT.getCode());
            }
        }

        // 姓名必填
        if (dto.getEmployeeName() == null || dto.getEmployeeName().trim().isEmpty()) {
            result.addError("姓名", "姓名必填", ValidationErrorType.REQUIRED.getCode());
        }

        // 身份证必填
        if (dto.getIdNumber() == null || dto.getIdNumber().trim().isEmpty()) {
            result.addError("身份证", "身份证必填", ValidationErrorType.REQUIRED.getCode());
        }

        // 减员操作时，手机号可能不是必填的，根据业务需求确定
        String operationType = dto.getOperationType();
        if (!"减员".equals(operationType)) {
            // 增员和更正操作时，手机号必填
            if (dto.getMobile() == null || dto.getMobile().trim().isEmpty()) {
                result.addError("手机号", "手机号必填", ValidationErrorType.REQUIRED.getCode());
            }
        }
    }

    /**
     * 格式校验（收集所有错误版本）
     */
    private void validateFieldFormatsCollectErrors(BaseDetailExportDTO dto, FieldValidationResult result) {
        // 身份证格式校验（所有操作都需要）
        if (dto.getIdNumber() != null && !dto.getIdNumber().trim().isEmpty()) {
            if (!ValidateUtil.isValidIdNumber(dto.getIdNumber())) {
                result.addError("身份证", "身份证格式不正确", ValidationErrorType.FORMAT.getCode());
            }
        }

        // 手机号格式校验（如果有手机号的话）
        if (dto.getMobile() != null && !dto.getMobile().trim().isEmpty()) {
            if (!dto.getMobile().matches("^1[3-9]\\d{9}$")) {
                result.addError("手机号", "手机号格式不正确", ValidationErrorType.FORMAT.getCode());
            }
        }

        // 社保基数格式校验（所有操作都需要，但减员可以为空）
        if (dto.getSocialInsuranceBase() != null && !dto.getSocialInsuranceBase().trim().isEmpty()) {
            try {
                BigDecimal base = new BigDecimal(dto.getSocialInsuranceBase());
                if (base.compareTo(BigDecimal.ZERO) <= 0) {
                    result.addError("社保基数", "社保基数必须大于0", ValidationErrorType.FORMAT.getCode());
                } else {
                    // 校验并修正社保基数为2位小数
                    BigDecimal formattedBase = base.setScale(2, RoundingMode.HALF_UP);
                    dto.setSocialInsuranceBase(formattedBase.toString());
                }
            } catch (NumberFormatException e) {
                result.addError("社保基数", "社保基数格式不正确", ValidationErrorType.FORMAT.getCode());
            }
        }

        // 个税特有字段格式校验（如果是个税明细）
        if (dto instanceof PersonalTaxDetailExportDTO) {
            validatePersonalTaxFieldFormats((PersonalTaxDetailExportDTO) dto, result);
        } else if (dto instanceof PersonalTaxDetailImportDTO) {
            validatePersonalTaxFieldFormats((PersonalTaxDetailImportDTO) dto, result);
        }
    }


    /**
     * 个税特有字段格式校验（PersonalTaxDetailExportDTO版本）
     */
    private void validatePersonalTaxFieldFormats(PersonalTaxDetailExportDTO dto, FieldValidationResult result) {
        // 应发工资格式校验
        if (dto.getGrossSalary() != null && !dto.getGrossSalary().trim().isEmpty()) {
            try {
                BigDecimal salary = new BigDecimal(dto.getGrossSalary());
                if (salary.compareTo(BigDecimal.ZERO) < 0) {
                    result.addError("应发工资", "应发工资不能为负数", ValidationErrorType.FORMAT.getCode());
                } else {
                    // 校验并修正应发工资为2位小数
                    BigDecimal formattedSalary = salary.setScale(2, RoundingMode.HALF_UP);
                    dto.setGrossSalary(formattedSalary.toString());
                }
            } catch (NumberFormatException e) {
                result.addError("应发工资", "应发工资格式不正确", ValidationErrorType.FORMAT.getCode());
            }
        }

        // 公积金个人缴存金额格式校验
        if (dto.getHousingFundPersonalAmount() != null && !dto.getHousingFundPersonalAmount().trim().isEmpty()) {
            try {
                BigDecimal amount = new BigDecimal(dto.getHousingFundPersonalAmount());
                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    result.addError("公积金个人缴存金额", "公积金个人缴存金额不能为负数", ValidationErrorType.FORMAT.getCode());
                } else {
                    // 校验并修正公积金金额为2位小数
                    BigDecimal formattedAmount = amount.setScale(2, RoundingMode.HALF_UP);
                    dto.setHousingFundPersonalAmount(formattedAmount.toString());
                }
            } catch (NumberFormatException e) {
                result.addError("公积金个人缴存金额", "公积金个人缴存金额格式不正确", ValidationErrorType.FORMAT.getCode());
            }
        }
    }

    /**
     * 个税特有字段格式校验（PersonalTaxDetailImportDTO版本）
     */
    private void validatePersonalTaxFieldFormats(PersonalTaxDetailImportDTO dto, FieldValidationResult result) {
        // 应发工资格式校验
        if (dto.getGrossSalary() != null && !dto.getGrossSalary().trim().isEmpty()) {
            try {
                BigDecimal salary = new BigDecimal(dto.getGrossSalary());
                if (salary.compareTo(BigDecimal.ZERO) < 0) {
                    result.addError("应发工资", "应发工资不能为负数", ValidationErrorType.FORMAT.getCode());
                } else {
                    // 校验并修正应发工资为2位小数
                    BigDecimal formattedSalary = salary.setScale(2, RoundingMode.HALF_UP);
                    dto.setGrossSalary(formattedSalary.toString());
                }
            } catch (NumberFormatException e) {
                result.addError("应发工资", "应发工资格式不正确", ValidationErrorType.FORMAT.getCode());
            }
        }

        // 公积金个人缴存金额格式校验
        if (dto.getHousingFundPersonalAmount() != null && !dto.getHousingFundPersonalAmount().trim().isEmpty()) {
            try {
                BigDecimal amount = new BigDecimal(dto.getHousingFundPersonalAmount());
                if (amount.compareTo(BigDecimal.ZERO) < 0) {
                    result.addError("公积金个人缴存金额", "公积金个人缴存金额不能为负数", ValidationErrorType.FORMAT.getCode());
                } else {
                    // 校验并修正公积金金额为2位小数
                    BigDecimal formattedAmount = amount.setScale(2, RoundingMode.HALF_UP);
                    dto.setHousingFundPersonalAmount(formattedAmount.toString());
                }
            } catch (NumberFormatException e) {
                result.addError("公积金个人缴存金额", "公积金个人缴存金额格式不正确", ValidationErrorType.FORMAT.getCode());
            }
        }
    }

    /**
     * 通用字段重复校验方法
     *
     * @param fieldValue 字段值
     * @param fieldName 字段名称（用于错误提示）
     * @param batchSet 当前Excel批次中的字段值集合
     * @param existingSet 数据库中已存在的字段值集合
     * @param overrideExisting 是否为覆盖模式
     * @param result 校验结果收集器
     */
    private void validateFieldDuplicate(String fieldValue, String fieldName,
                                      Set<String> batchSet, Set<String> existingSet,
                                      boolean overrideExisting, FieldValidationResult result) {
        if (fieldValue == null || fieldValue.trim().isEmpty()) {
            return;
        }

        // 1. 校验Excel内部重复（所有模式都需要校验）
        if (batchSet.contains(fieldValue)) {
            result.addError(fieldName, fieldName + "已存在", ValidationErrorType.DUPLICATE.getCode());
            return;
        }

        // 2. 校验数据库重复（仅在非覆盖模式下校验）
        if (!overrideExisting && existingSet.contains(fieldValue)) {
            result.addError(fieldName, fieldName + "已存在", ValidationErrorType.DUPLICATE.getCode());
            return;
        }

        // 3. 首次出现则记录在批次集合中
        batchSet.add(fieldValue);
    }

    /**
     * 去重校验（收集所有错误版本）
     */
    private void validateDuplicatesCollectErrors(BaseDetailExportDTO dto, ValidationContext ctx, FieldValidationResult result) {
        // 身份证号去重校验
        validateFieldDuplicate(dto.getIdNumber(), "身份证号",
                             ctx.batchIdNumbers, ctx.existingIdNumbers,
                             ctx.overrideExisting, result);

        // 手机号去重校验
        validateFieldDuplicate(dto.getMobile(), "手机号",
                             ctx.batchMobiles, ctx.existingMobiles,
                             ctx.overrideExisting, result);
    }

    /**
     * 业务规则校验（收集所有错误版本）
     */
    private void validateBusinessRulesCollectErrors(BaseDetailExportDTO dto, Integer bizType, FieldValidationResult result) {
        String operationType = dto.getOperationType();

        // 减员操作跳过业务规则校验，只需要基本信息即可
        if ("减员".equals(operationType)) {
            return;
        }

        // 社保基数必填（增员和更正操作）
        if (dto.getSocialInsuranceBase() == null || dto.getSocialInsuranceBase().trim().isEmpty()) {
            result.addError("社保基数", "社保基数必填", ValidationErrorType.BUSINESS.getCode());
        }

        // 申报险种至少要选一个
        //if (!hasAnyInsurance(dto)) {
        //    result.addError("申报险种", "至少需要选择一种保险", ValidationErrorType.BUSINESS.getCode());
        //}

        // 当选择其他保险时，备注必填（保留此校验逻辑）
        if ("是".equals(dto.getQiTa())) {
            String remark = dto.getRemark();
            if (remark == null || remark.trim().isEmpty()) {
                result.addError("备注", "当选择其他保险时，备注必填", ValidationErrorType.BUSINESS.getCode());
            }
        }

        // 个税明细特有业务规则校验
        if (bizType.equals(2) && (dto instanceof PersonalTaxDetailImportDTO || dto instanceof PersonalTaxDetailExportDTO)) {
            validatePersonalTaxBusinessRules(dto, result);
        }
    }

    /**
     * 个税明细特有业务规则校验
     */
    private void validatePersonalTaxBusinessRules(BaseDetailExportDTO dto, FieldValidationResult result) {
        String grossSalary = null;
        String housingFundPersonalAmount = null;

        // 获取个税特有字段值
        if (dto instanceof PersonalTaxDetailImportDTO) {
            PersonalTaxDetailImportDTO personalTaxDTO = (PersonalTaxDetailImportDTO) dto;
            grossSalary = personalTaxDTO.getGrossSalary();
            housingFundPersonalAmount = personalTaxDTO.getHousingFundPersonalAmount();
        } else if (dto instanceof PersonalTaxDetailExportDTO) {
            PersonalTaxDetailExportDTO personalTaxDTO = (PersonalTaxDetailExportDTO) dto;
            grossSalary = personalTaxDTO.getGrossSalary();
            housingFundPersonalAmount = personalTaxDTO.getHousingFundPersonalAmount();
        }

        // 应发工资必填
        if (grossSalary == null || grossSalary.trim().isEmpty()) {
            result.addError("应发工资", "应发工资必填", ValidationErrorType.BUSINESS.getCode());
        }

        // 公积金个人缴存金额可以为空，但如果填写了需要校验合理性
        if (housingFundPersonalAmount != null && !housingFundPersonalAmount.trim().isEmpty() &&
            grossSalary != null && !grossSalary.trim().isEmpty()) {
            try {
                BigDecimal housingFund = new BigDecimal(housingFundPersonalAmount);
                BigDecimal salary = new BigDecimal(grossSalary);

                // 公积金缴存金额不应超过应发工资
                if (housingFund.compareTo(salary) > 0) {
                    result.addError("公积金个人缴存金额", "公积金个人缴存金额不应超过应发工资", ValidationErrorType.BUSINESS.getCode());
                }
            } catch (NumberFormatException e) {
                // 格式错误在格式校验阶段已经处理，这里不重复添加
            }
        }
    }

    /**
     * 检查是否有任何保险
     */
    private boolean hasAnyInsurance(BaseDetailExportDTO dto) {
        try {
            return "是".equals(dto.getYangLao()) ||
                   "是".equals(dto.getShiYe()) ||
                   "是".equals(dto.getGongShang()) ||
                   "是".equals(dto.getYiLiao()) ||
                   "是".equals(dto.getShengYu());
        } catch (Exception e) {
            log.warn("Error checking insurance: {}", e.getMessage());
            return true; // 出错时默认通过
        }
    }

    /**
     * 转换导入DTO为 ValueAddedEmployee 实体
     */
    private ValueAddedEmployee convertToEmployee(BaseDetailExportDTO dto, Integer bizType, String deliveryOrderNo) {
        ValueAddedEmployee employee = new ValueAddedEmployee();

        try {
            // 设置基础信息
            employee.setEmployeeName(dto.getEmployeeName());
            employee.setIdNumber(dto.getIdNumber());
            employee.setMobile(dto.getMobile());
            employee.setDeliveryOrderNo(deliveryOrderNo);
            employee.setBizType(bizType);
            employee.setRemark(dto.getRemark());

            // 设置操作类型
            String operationType = dto.getOperationType();
            if (operationType != null) {
                switch (operationType) {
                    case "增员":
                        employee.setOperationType(1);
                        break;
                    case "更正":
                        employee.setOperationType(2);
                        break;
                    case "减员":
                        employee.setOperationType(3);
                        break;
                    default:
                        employee.setOperationType(1); // 默认为增员
                }
            }

            // 设置社保基数
            String socialInsuranceBaseStr = dto.getSocialInsuranceBase();
            if (socialInsuranceBaseStr != null && !socialInsuranceBaseStr.trim().isEmpty()) {
                try {
                    BigDecimal socialInsuranceBase = new BigDecimal(socialInsuranceBaseStr);
                    employee.setSocialInsuranceBase(socialInsuranceBase);
                    employee.setGrossSalary(socialInsuranceBase); // 简化处理
                } catch (NumberFormatException e) {
                    log.warn("Invalid social insurance base format: {}", socialInsuranceBaseStr);
                }
            }
            if (dto instanceof PersonalTaxDetailImportDTO) {
                PersonalTaxDetailImportDTO personalTaxDTO = (PersonalTaxDetailImportDTO) dto;
                // 设置公积金
                String housingFundPersonalAmountStr = personalTaxDTO.getHousingFundPersonalAmount();
                if (housingFundPersonalAmountStr != null && !housingFundPersonalAmountStr.trim().isEmpty()) {
                    try {
                        BigDecimal housingFundPersonalAmount = new BigDecimal(housingFundPersonalAmountStr);
                        employee.setProvidentFundPersonal(housingFundPersonalAmount);
                    } catch (NumberFormatException e) {
                        log.warn("Invalid provident fund personal format: {}", socialInsuranceBaseStr);
                    }
                }
            }
            // 构建社保信息对象并设置
            SocialInsuranceVO socialInsurance = buildSocialInsuranceFromImportDTO(dto);
            employee.setSocialInsurance(socialInsurance);

            // 设置录入方式为批量新增
            employee.setEntryType(1);

        } catch (Exception e) {
            log.error("Error converting DTO to employee: {}", e.getMessage(), e);
            throw new RuntimeException("数据转换失败: " + e.getMessage());
        }

        return employee;
    }
}
