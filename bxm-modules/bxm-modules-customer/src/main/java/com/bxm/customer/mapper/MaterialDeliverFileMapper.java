package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.MaterialDeliverFile;

/**
 * 材料交接单文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-08
 */
@Mapper
public interface MaterialDeliverFileMapper extends BaseMapper<MaterialDeliverFile>
{
    /**
     * 查询材料交接单文件
     * 
     * @param id 材料交接单文件主键
     * @return 材料交接单文件
     */
    public MaterialDeliverFile selectMaterialDeliverFileById(Long id);

    /**
     * 查询材料交接单文件列表
     * 
     * @param materialDeliverFile 材料交接单文件
     * @return 材料交接单文件集合
     */
    public List<MaterialDeliverFile> selectMaterialDeliverFileList(MaterialDeliverFile materialDeliverFile);

    /**
     * 新增材料交接单文件
     * 
     * @param materialDeliverFile 材料交接单文件
     * @return 结果
     */
    public int insertMaterialDeliverFile(MaterialDeliverFile materialDeliverFile);

    /**
     * 修改材料交接单文件
     * 
     * @param materialDeliverFile 材料交接单文件
     * @return 结果
     */
    public int updateMaterialDeliverFile(MaterialDeliverFile materialDeliverFile);

    /**
     * 删除材料交接单文件
     * 
     * @param id 材料交接单文件主键
     * @return 结果
     */
    public int deleteMaterialDeliverFileById(Long id);

    /**
     * 批量删除材料交接单文件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaterialDeliverFileByIds(Long[] ids);
}
