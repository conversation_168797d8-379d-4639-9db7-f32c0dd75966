package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.QualityCheckingItem;
import org.apache.ibatis.annotations.Param;

/**
 * 质检项目配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-12
 */
@Mapper
public interface QualityCheckingItemMapper extends BaseMapper<QualityCheckingItem>
{
    /**
     * 查询质检项目配置
     * 
     * @param id 质检项目配置主键
     * @return 质检项目配置
     */
    public QualityCheckingItem selectQualityCheckingItemById(Long id);

    /**
     * 查询质检项目配置列表
     * 
     * @param qualityCheckingItem 质检项目配置
     * @return 质检项目配置集合
     */
    public List<QualityCheckingItem> selectQualityCheckingItemList(QualityCheckingItem qualityCheckingItem);

    /**
     * 新增质检项目配置
     * 
     * @param qualityCheckingItem 质检项目配置
     * @return 结果
     */
    public int insertQualityCheckingItem(QualityCheckingItem qualityCheckingItem);

    /**
     * 修改质检项目配置
     * 
     * @param qualityCheckingItem 质检项目配置
     * @return 结果
     */
    public int updateQualityCheckingItem(QualityCheckingItem qualityCheckingItem);

    /**
     * 删除质检项目配置
     * 
     * @param id 质检项目配置主键
     * @return 结果
     */
    public int deleteQualityCheckingItemById(Long id);

    /**
     * 批量删除质检项目配置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQualityCheckingItemByIds(Long[] ids);

    List<QualityCheckingItem> qualityCheckingItemSelect(@Param("qualityCheckingType") Integer qualityCheckingType,
                                                        @Param("qualityCheckingCycle") Integer qualityCheckingCycle);
}
