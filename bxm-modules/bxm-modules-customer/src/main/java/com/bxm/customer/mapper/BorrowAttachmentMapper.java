package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.BorrowAttachment;

/**
 * 借阅附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-20
 */
@Mapper
public interface BorrowAttachmentMapper extends BaseMapper<BorrowAttachment>
{
    /**
     * 查询借阅附件
     * 
     * @param id 借阅附件主键
     * @return 借阅附件
     */
    public BorrowAttachment selectBorrowAttachmentById(Long id);

    /**
     * 查询借阅附件列表
     * 
     * @param borrowAttachment 借阅附件
     * @return 借阅附件集合
     */
    public List<BorrowAttachment> selectBorrowAttachmentList(BorrowAttachment borrowAttachment);

    /**
     * 新增借阅附件
     * 
     * @param borrowAttachment 借阅附件
     * @return 结果
     */
    public int insertBorrowAttachment(BorrowAttachment borrowAttachment);

    /**
     * 修改借阅附件
     * 
     * @param borrowAttachment 借阅附件
     * @return 结果
     */
    public int updateBorrowAttachment(BorrowAttachment borrowAttachment);

    /**
     * 删除借阅附件
     * 
     * @param id 借阅附件主键
     * @return 结果
     */
    public int deleteBorrowAttachmentById(Long id);

    /**
     * 批量删除借阅附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBorrowAttachmentByIds(Long[] ids);
}
