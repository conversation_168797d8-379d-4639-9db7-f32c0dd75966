### 批量删除增值交付单测试
### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/x-www-form-urlencoded
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjViOWU2ZTM3LTkyOTAtNDlkZC1hMzA1LWQ3NTRhOWEyYTA0MyIsInVzZXJuYW1lIjoiYWRtaW4ifQ.6Agj41bjdrmdXvZazvj9NroySbl9tY4rotgr0UJj4CN1ShIGHww59uLCiX-AXmQaX1T8cRUo5jaAGnTnCp_LLQ

### 批量删除增值交付单 - 标准测试
POST {{baseUrl}}/customer/valueAdded/deliveryOrder/batchDelete
Content-Type: {{contentType}}
Authorization: {{authorization}}

deliveryOrderNos=SW250826215432636CU&deliveryOrderNos=VAD2508051430002A1C&operatorId=1001&operatorName=张三&reason=批量删除测试

> {%
client.test("批量删除请求", function() {
    client.assert(response.status === 200, "响应状态码应为200");
    client.assert(response.contentType.mimeType === "application/json", "响应类型应为JSON");
    
    var responseBody = response.body;
    client.assert(responseBody.hasOwnProperty("code"), "响应应包含code字段");
    client.assert(responseBody.hasOwnProperty("msg"), "响应应包含msg字段");
    
    if (responseBody.code === 200) {
        client.log("批量删除成功: " + responseBody.msg);
    } else {
        client.log("批量删除失败: " + responseBody.msg);
    }
});
%}
