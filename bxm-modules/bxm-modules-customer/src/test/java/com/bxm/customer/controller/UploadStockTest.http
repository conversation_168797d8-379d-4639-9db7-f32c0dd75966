### 上传库存Excel文件接口测试 - uploadStock方法

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = multipart/form-data
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6ImQ2MTNlMmQ5LTAzZjUtNDI2Ny1hZjQ5LTYyYzM5NjkwMDg5MSIsInVzZXJuYW1lIjoiYWRtaW4ifQ.K9s3YbMAYoHZ2rwI8qWs0MO7qv8wGlc49OKV3FEAFYNbt5EXj5YpbjbTzlh3trI3bpzxXbp01WBu3x598dILRw

### 注意：如果token过期，请通过登录接口获取新的token并更新上述@authorization变量

### 1. 正常上传库存Excel文件测试 (.xlsx格式)
POST {{baseUrl}}/valueAddedFile/uploadStock
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryUploadStock1
Authorization: {{authorization}}

------WebKitFormBoundaryUploadStock1
Content-Disposition: form-data; name="deliveryOrderNo"

SW250904225041613DN
------WebKitFormBoundaryUploadStock1
Content-Disposition: form-data; name="file"; filename="stock_test.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./testttt.xlsx
------WebKitFormBoundaryUploadStock1--
