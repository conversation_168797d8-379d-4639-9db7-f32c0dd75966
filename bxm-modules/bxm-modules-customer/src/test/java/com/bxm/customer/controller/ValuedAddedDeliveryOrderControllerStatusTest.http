### 增值交付单状态管理测试文件
### 测试ValuedAddedDeliveryOrderController的状态管理功能
### 包括状态变更和获取可用状态接口

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = application/json
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6IjU4MDE3NGRhLWIxMWItNDJjOC1iOTZmLWRlYTI3MjM1YTk3NyIsInVzZXJuYW1lIjoiYWRtaW4ifQ.cfjCTWFKB07-XkeJkGu-w0Ofq0TJeRWp8276Qh35b5mJLohjwu5l51eodUyOZEj7qtAiWHmow4aH4W1pk33Qpg

### ========================================
### 0. 测试策略覆盖情况（调试接口）
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/debug/strategyCoverage
Authorization: {{authorization}}

### ========================================
### 1. 测试获取可用状态列表
### ========================================
GET {{baseUrl}}/valuedAddedDeliveryOrder/availableStatuses/VAD2508051430001A1C
Authorization: {{authorization}}

### ========================================
### 2. 测试状态变更 - 从草稿到待提交
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "SW250831232822994SK",
  "valueAddedItemTypeId": 7,
  "creditCode": "123213",
  "deliveryFiles": [
    {
      "fileSize": 793,
      "fileName": "批里刚除异常数据 (1).xlsx",
      "url": "2025/09/01/0ca8527c20431c1941d6600a6e03a692.xlsx",
      "fileId": 296,
      "fileName": "批里刚除异常数据 (1).xlsx",
      "fileSize": 793,
      "fileType": null,
      "fileUrl": null,
      "fullUrl": "https://zbbfile.sikilab.com/2025/09/01/0ca8527c20431c1941d6600a6e03a692.xlsx",
      "status": "success",
      "uid": "1756658337701",
      "url": "2025/09/01/0ca8527c20431c1941d6600a6e03a692.xlsx"
    }
  ],
  "deliveryOrderNo": "SW250831232822994SK",
  "operationAttachments": [
    {
      "uid": "1756658349507",
      "name": "joinUs-boy.png",
      "file": {
        "uid": "1756658349507"
      },
      "fileName": "joinUs-boy.png",
      "fileSize": 84453,
      "fileUrl": "2025/09/01/4ffba614829b36be08f44d20654e7763.png",
      "fullFileUrl": "https://zbbfile.sikilab.com/2025/09/01/4ffba614829b36be08f44d20654e7763.png",
      "name": "joinUs-boy.png",
      "percentage": 100,
      "status": "success",
      "uid": "1756658349507"
    }
  ],
  "remark": "操作的备注呢",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "valueAddedItemTypeId": 7
}
### ========================================
### 3. 测试状态变更 - 从待提交到已提交待交付
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "SUBMITTED_PENDING_DELIVERY",
  "reason": "资料审核通过，提交交付",
  "operatorId": 1,
  "operatorName": "李四",
  "remark": "审核通过"
}

### ========================================
### 4. 测试状态变更 - 从已提交待交付到已交付待确认
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "PENDING_CONFIRMATION",
  "reason": "交付完成，等待会计确认",
  "operatorId": 2,
  "operatorName": "王五",
  "remark": "交付完成"
}

### ========================================
### 5. 测试状态变更 - 从已交付待确认到已确认待扣款
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "CONFIRMED_PENDING_DEDUCTION",
  "reason": "会计确认无误，可以扣款",
  "operatorId": 3,
  "operatorName": "赵六",
  "remark": "会计确认"
}

### ========================================
### 6. 测试状态变更 - 从已确认待扣款到已扣款
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "DEDUCTION_COMPLETED",
  "reason": "扣款成功",
  "operatorId": 4,
  "operatorName": "财务系统",
  "remark": "自动扣款完成"
}

### ========================================
### 7. 测试异常状态变更 - 扣款异常
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430002C2D",
  "targetStatus": "DEDUCTION_EXCEPTION",
  "reason": "客户账户余额不足，扣款失败",
  "operatorId": 4,
  "operatorName": "财务系统",
  "remark": "需要人工处理扣款异常"
}

### ========================================
### 8. 测试异常状态变更 - 交付异常
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430003E3F",
  "targetStatus": "DELIVERY_EXCEPTION",
  "reason": "客户提供的资料不完整，无法完成交付",
  "operatorId": 2,
  "operatorName": "交付专员",
  "remark": "需要客户补充资料"
}

### ========================================
### 9. 测试错误场景 - 无效的状态转换
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD2508051430001A1C",
  "targetStatus": "DRAFT",
  "reason": "尝试无效的状态转换",
  "operatorId": 1,
  "operatorName": "测试用户",
  "remark": "测试错误场景"
}

### ========================================
### 10. 测试错误场景 - 不存在的交付单
### ========================================
POST {{baseUrl}}/valuedAddedDeliveryOrder/changeStatus
Content-Type: {{contentType}}
Authorization: {{authorization}}

{
  "deliveryOrderNo": "VAD9999999999999999",
  "targetStatus": "SAVED_PENDING_SUBMIT",
  "reason": "测试不存在的交付单",
  "operatorId": 1,
  "operatorName": "测试用户",
  "remark": "测试错误场景"
}
