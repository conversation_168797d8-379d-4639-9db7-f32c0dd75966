### ValueAddedFileController HTTP 测试文件
### 增值交付单文件管理接口测试

### 环境变量配置
@baseUrl = http://localhost:8085/bxmCustomer
@contentType = multipart/form-data
@authorization = Bearer eyJhbGciOiJIUzUxMiJ9.eyJ1c2VyX2lkIjoxLCJ1c2VyX2tleSI6Ijc3MjMwMDllLTdlNzUtNDY5Yi04MTNiLTk0ZGRiMDRkZDdjNiIsInVzZXJuYW1lIjoiYWRtaW4ifQ.idjbkNsZfIbu5DUQutG9T5AQyVzKPp2kM9gZBozdeTi5bvKCtWoS_a8T0f-oy0eVDcg1ecreFimp-z_9Hx-Avg


### 注意：如果token过期，请通过登录接口获取新的token并更新上述@authorization变量

### 1. 正常上传交付单文件测试
POST {{baseUrl}}/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Authorization: {{authorization}}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO20250818001
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 2. 缺少交付单编号测试
POST {{baseUrl}}/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Authorization: {{authorization}}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 3. 空交付单编号测试
POST {{baseUrl}}/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Authorization: {{authorization}}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="2025-07-29T230358.200.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./2025-07-29T230358.200.xlsx
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 4. 缺少文件测试
POST {{baseUrl}}/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Authorization: {{authorization}}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="deliveryOrderNo"

DO20250818001
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 5. 使用简化的表单数据格式测试（推荐用于IDE测试）
POST {{baseUrl}}/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data
Authorization: {{authorization}}

deliveryOrderNo=DO20250818002&file=< ./2025-07-29T230358.200.xlsx

### 6. 测试不同的交付单编号格式
POST {{baseUrl}}/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data
Authorization: {{authorization}}

deliveryOrderNo=TEST-ORDER-123&file=< ./2025-07-29T230358.200.xlsx

### 7. 测试长交付单编号
POST {{baseUrl}}/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data
Authorization: {{authorization}}

deliveryOrderNo=VERY_LONG_DELIVERY_ORDER_NUMBER_FOR_TESTING_PURPOSES_2025&file=< ./2025-07-29T230358.200.xlsx

### 8. 测试包含特殊字符的交付单编号
POST {{baseUrl}}/valueAddedFile/uploadDeliveryOrderFile
Content-Type: multipart/form-data
Authorization: {{authorization}}

deliveryOrderNo=DO-2025/08/18-001&file=< ./2025-07-29T230358.200.xlsx

###
### 测试说明：
### 1. 第1个测试用例是正常的文件上传，应该返回成功结果和文件ID
### 2. 第2个测试用例缺少交付单编号，应该返回"交付单编号不能为空"错误
### 3. 第3个测试用例交付单编号为空，应该返回"交付单编号不能为空"错误
### 4. 第4个测试用例缺少文件，应该返回"文件不能为空"错误
### 5. 第5-8个测试用例使用简化格式测试不同的交付单编号场景
###
### 预期响应格式：
### 成功: {"code": 200, "msg": "文件上传成功", "data": 文件ID}
### 失败: {"code": 500, "msg": "错误信息", "data": null}
###
### 9. 上传文件 - 交付材料附件 (bizFileType=1)
POST {{baseUrl}}/valueAddedFile/uploadFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryUploadFile1
Authorization: {{authorization}}

------WebKitFormBoundaryUploadFile1
Content-Disposition: form-data; name="deliveryOrderNo"

DO20250818001
------WebKitFormBoundaryUploadFile1
Content-Disposition: form-data; name="bizFileType"

1
------WebKitFormBoundaryUploadFile1
Content-Disposition: form-data; name="file"; filename="testttt.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./testttt.xlsx
------WebKitFormBoundaryUploadFile1--

### 10. 上传文件 - 人员明细excel (bizFileType=2)
POST {{baseUrl}}/valueAddedFile/uploadFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryUploadFile2
Authorization: {{authorization}}

------WebKitFormBoundaryUploadFile2
Content-Disposition: form-data; name="deliveryOrderNo"

DO20250818001
------WebKitFormBoundaryUploadFile2
Content-Disposition: form-data; name="bizFileType"

2
------WebKitFormBoundaryUploadFile2
Content-Disposition: form-data; name="file"; filename="testttt.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./testttt.xlsx
------WebKitFormBoundaryUploadFile2--

### 11. 上传文件 - 库存Excel (bizFileType=3)
POST {{baseUrl}}/valueAddedFile/uploadFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryUploadFile3
Authorization: {{authorization}}

------WebKitFormBoundaryUploadFile3
Content-Disposition: form-data; name="deliveryOrderNo"

DO20250818001
------WebKitFormBoundaryUploadFile3
Content-Disposition: form-data; name="bizFileType"

3
------WebKitFormBoundaryUploadFile3
Content-Disposition: form-data; name="file"; filename="testttt.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./testttt.xlsx
------WebKitFormBoundaryUploadFile3--

### 12. 上传文件 - 操作附件 (bizFileType=4)
POST {{baseUrl}}/valueAddedFile/uploadFile
Content-Type: multipart/form-data; boundary=----WebKitFormBoundaryUploadFile4
Authorization: {{authorization}}

------WebKitFormBoundaryUploadFile4
Content-Disposition: form-data; name="deliveryOrderNo"

DO20250818001
------WebKitFormBoundaryUploadFile4
Content-Disposition: form-data; name="bizFileType"

4
------WebKitFormBoundaryUploadFile4
Content-Disposition: form-data; name="file"; filename="testttt.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ./testttt.xlsx
------WebKitFormBoundaryUploadFile4--
